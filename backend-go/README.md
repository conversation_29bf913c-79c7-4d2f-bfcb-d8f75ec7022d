# VPN Shop - Backend API

Backend service untuk aplikasi VPN Shop yang dibangun menggunakan **Clean Architecture** dengan bahasa Go. Sistem ini mengelola pembelian layanan VPN, manajemen server, sistem pembayaran, dan otentikasi pengguna dengan arsitektur yang scalable dan maintainable.

## 🚀 Fitur Utama

- **Manajemen Pengguna**: Registrasi, login, profil, dan sistem role
- **Layanan VPN**: Pembelian trial, hourly, dan monthly untuk berbagai protokol (VMess, VLess, Trojan)
- **Sistem Pembayaran**: Integrasi dengan Tripay payment gateway dan top-up saldo
- **Manajemen Server**: CRUD server VPN dengan monitoring status
- **Billing Otomatis**: Sistem billing per jam dengan cron jobs
- **Notifikasi**: Sistem notifikasi untuk transaksi dan aktivitas pengguna
- **Admin Panel**: Dashboard admin untuk manajemen sistem

## 🛠️ Tech Stack

- **Bahasa**: [Go](https://golang.org/) 1.24+
- **Arsitektur**: Clean Architecture (Domain-Driven Design)
- **Framework Web**: [Echo](https://echo.labstack.com/) v4
- **ORM**: [GORM](https://gorm.io/) v2
- **Database**: PostgreSQL
- **Validasi**: [Go Playground Validator](https://github.com/go-playground/validator) v10
- **Otentikasi**: JWT (JSON Web Tokens)
- **Dokumentasi API**: [Swagger](https://swagger.io/) dengan Go annotations
- **Cron Jobs**: [gocron](https://github.com/go-co-op/gocron) v2
- **Payment Gateway**: Tripay API Integration

---

## 🚀 Quick Start

### Prerequisites

- **Go** 1.24+ ([Download](https://golang.org/dl/))
- **PostgreSQL** 12+ ([Download](https://www.postgresql.org/download/))
- **Git** untuk clone repository

### Installation & Setup

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd vpn-shop/backend-go
   ```

2. **Environment Configuration**

   Buat file `.env` di root directory dengan konfigurasi berikut:
   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_PORT=5432
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_NAME=vpn_shop

   # Server Configuration
   SERVER_HOST=localhost
   SERVER_PORT=8000

   # JWT Configuration
   JWT_SECRET_KEY=your_super_secret_jwt_key
   ACCESS_TOKEN_EXPIRE_MINUTES=1440

   # Admin User (akan dibuat otomatis)
   FIRST_SUPERUSER_NAME=Admin
   FIRST_SUPERUSER_USERNAME=admin
   FIRST_SUPERUSER_EMAIL=<EMAIL>
   FIRST_SUPERUSER_PASSWORD=admin123

   # CORS Configuration
   BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:3001
   ```

3. **Install Dependencies**
   ```bash
   go mod tidy
   ```

4. **Database Setup**

   Pastikan PostgreSQL berjalan dan database sudah dibuat:
   ```sql
   CREATE DATABASE vpn_shop;
   ```

5. **Run Application**
   ```bash
   # Development mode
   go run cmd/server/main.go

   # Build dan run
   go build -o bin/server cmd/server/main.go
   ./bin/server
   ```

6. **Verify Installation**

   Server akan berjalan di `http://localhost:8000`
   - API Documentation: `http://localhost:8000/swagger/index.html`
   - Health Check: `http://localhost:8000/`

---

## 📖 API Documentation & Integration Guide

### 🔐 Authentication Flow

#### 1. User Registration
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "password123",
  "whatsapp": "************"
}
```

#### 2. User Login
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "johndoe",
    "email": "<EMAIL>",
    "name": "John Doe"
  }
}
```

#### 3. Accessing Protected Endpoints
Sertakan JWT token di header untuk setiap request ke endpoint yang memerlukan otentikasi:

```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 4. Error Handling
- **401 Unauthorized**: Token tidak valid atau expired
- **403 Forbidden**: User tidak memiliki permission
- **422 Validation Error**: Input data tidak valid

### 📋 API Response Format

API menggunakan format respons yang konsisten untuk memudahkan integrasi frontend.

#### ✅ Success Responses

**Data Retrieval (GET):**
```json
{
  "id": 1,
  "username": "johndoe",
  "email": "<EMAIL>",
  "saldo": 50000
}
```

**Action Confirmation (POST/PUT/DELETE):**
```json
{
  "message": "Data berhasil disimpan"
}
```

**List Data with Pagination:**
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "total_pages": 10
  }
}
```

#### ❌ Error Responses

Semua error menggunakan struktur yang konsisten:

```json
{
  "error": "Pesan error yang jelas dalam Bahasa Indonesia"
}
```

**Common HTTP Status Codes:**
- `400 Bad Request`: Input validation error
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Data conflict (duplicate, etc.)
- `422 Unprocessable Entity`: Business logic error
- `500 Internal Server Error`: Server error

**Example Error Responses:**
```json
// 400 - Validation Error
{
  "error": "Email tidak boleh kosong"
}

// 401 - Authentication Error
{
  "error": "Token JWT tidak valid atau tidak ditemukan"
}

// 404 - Not Found
{
  "error": "Pengguna tidak ditemukan"
}

// 409 - Conflict
{
  "error": "Username sudah digunakan"
}
```

### 📚 API Documentation (Swagger)

Dokumentasi lengkap tersedia melalui Swagger UI:

**🔗 URL**: `http://localhost:8000/swagger/index.html`

Swagger menyediakan:
- ✅ Interactive API testing
- ✅ Request/response examples
- ✅ Authentication testing
- ✅ Parameter validation
- ✅ Real-time API exploration

### 🛣️ Main API Endpoints

#### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout

#### User Management
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/me` - Update user profile
- `POST /api/v1/users/me/photo` - Upload profile photo
- `GET /api/v1/users/me/stats` - Get user statistics

#### VPN Services
- `POST /api/v1/purchase/trial` - Create trial account
- `POST /api/v1/purchase/hourly` - Purchase hourly service
- `POST /api/v1/purchase/monthly` - Purchase monthly service
- `GET /api/v1/accounts/history` - Get purchase history

#### Payment & Billing
- `POST /api/v1/payment/topup` - Top-up user balance
- `POST /api/v1/payment/create` - Create payment transaction
- `GET /api/v1/users/me/transactions` - Get transaction history

#### Server Management
- `GET /api/v1/servers` - List available servers
- `GET /api/v1/servers/{id}` - Get server details
- `POST /api/v1/servers` - Create new server (Admin)

#### Account Management
- `GET /api/v1/accounts/detail/{username}/{server_code}` - Get account details
- `POST /api/v1/accounts/renew` - Renew account
- `DELETE /api/v1/accounts/{id}` - Delete account

**💡 Tip**: Gunakan Swagger UI untuk testing dan eksplorasi API secara interaktif!

---

## Arsitektur Proyek

Proyek ini menggunakan **Clean Architecture** untuk memastikan kode yang bersih, mudah ditest, dan mudah dipelihara. Struktur folder diorganisir berdasarkan layer tanggung jawab, bukan berdasarkan jenis file.

### Struktur Folder

```
backend-go/
├── cmd/
│   └── server/
│       └── main.go                    # Entry point aplikasi
├── internal/                          # Kode aplikasi private
│   ├── domain/                        # 🎯 CORE BUSINESS LOGIC
│   │   ├── entities/                  # Business entities/models
│   │   ├── repositories/              # Interface contracts untuk data access
│   │   └── usecases/                  # Business use cases
│   ├── infrastructure/                # 🔧 EXTERNAL CONCERNS
│   │   ├── http/                      # HTTP transport layer
│   │   │   ├── handlers/              # HTTP request handlers
│   │   │   ├── routes/                # Route definitions
│   │   │   └── middleware/            # HTTP middleware
│   │   ├── database/                  # Database layer
│   │   │   └── repositories/          # Repository implementations (GORM)
│   │   └── external/                  # Third-party services integration
│   └── application/                   # 🎮 APPLICATION LAYER
│       └── usecases/                  # Application use cases
├── pkg/                              # Public libraries/utilities
├── configs/                          # Configuration files
├── docs/                             # Documentation (Swagger)
├── uploads/                          # File uploads
└── logs/                             # Application logs
```

### Penjelasan Layer

#### 1. **Domain Layer** (`internal/domain/`)
- **Entities**: Struct data dan business rules yang tidak bergantung pada teknologi eksternal
- **Repositories**: Interface yang mendefinisikan kontrak untuk data access
- **Usecases**: Business logic murni tanpa ketergantungan pada framework

#### 2. **Infrastructure Layer** (`internal/infrastructure/`)
- **HTTP**: Semua yang berhubungan dengan transport HTTP (handlers, routes, middleware)
- **Database**: Implementasi repository menggunakan GORM
- **External**: Integrasi dengan service eksternal (payment gateway, notification, dll)

#### 3. **Application Layer** (`internal/application/`)
- **Usecases**: Orchestration layer yang menggabungkan domain logic dengan infrastructure

### Keuntungan Clean Architecture

1. **Independence**: Business logic tidak bergantung pada framework atau database
2. **Testability**: Mudah untuk membuat unit test karena dependency injection
3. **Maintainability**: Kode lebih mudah dipelihara dan dikembangkan
4. **Scalability**: Mudah menambah fitur baru tanpa merusak yang sudah ada
5. **Flexibility**: Mudah mengganti database atau framework tanpa mengubah business logic

### Dependency Flow

```
Infrastructure → Application → Domain
```

- **Domain** tidak bergantung pada layer lain
- **Application** hanya bergantung pada Domain
- **Infrastructure** bergantung pada Application dan Domain

### Contoh Implementasi

```go
// Domain Entity
type User struct {
    ID       uint   `json:"id"`
    Username string `json:"username"`
    Email    string `json:"email"`
}

// Domain Repository Interface
type UserRepository interface {
    GetByID(id uint) (*User, error)
    Create(user *User) error
}

// Infrastructure Repository Implementation
type userRepository struct {
    db *gorm.DB
}

func (r *userRepository) GetByID(id uint) (*User, error) {
    // GORM implementation
}

// Application Usecase
type UserUsecase struct {
    userRepo UserRepository
}

func (u *UserUsecase) GetUser(id uint) (*User, error) {
    return u.userRepo.GetByID(id)
}

// Infrastructure HTTP Handler
func GetUserHandler(userUsecase *UserUsecase) echo.HandlerFunc {
    return func(c echo.Context) error {
        // HTTP handling logic
    }
}
```
