# VPN Shop - Backend (Go)

Ini adalah layanan backend untuk aplikasi VPN Shop, dibangun menggunakan bahasa Go dengan framework Echo. Backend ini bertanggung jawab untuk mengelola data pengguna, server, otentikasi, dan logika bisnis lainnya.

## Tech Stack

- **Bahasa**: [Go](https://golang.org/) (versi 1.20+)
- **Framework Web**: [Echo](https://echo.labstack.com/) v4
- **ORM**: [GORM](https://gorm.io/)
- **Database**: PostgreSQL
- **Validasi**: [Validator](https://github.com/go-playground/validator)
- **Otentikasi**: JWT (JSON Web Tokens)
- **Dokumentasi API**: [Swagger](https://swagger.io/)

---

## Panduan Integrasi untuk Frontend

Bagian ini menjelaskan poin-poin penting yang perlu diketahui oleh tim frontend untuk berinteraksi dengan API backend.

### 1. Menjalankan Proyek Backend

Pastikan Anda memiliki Go dan PostgreSQL terinstal.

1.  **Clone repository**.
2.  **Setup Environment Variables**: Buat file `.env` di root proyek dan isi variabel yang dibutuhkan (lihat `main.go` untuk daftar variabel yang digunakan, seperti `DB_USER`, `DB_PASSWORD`, `DB_NAME`, `JWT_SECRET`).
3.  **Install Dependencies**:
    ```bash
    go mod tidy
    ```
4.  **Jalankan Server**:
    ```bash
    go run cmd/server/main.go
    ```
    Server akan berjalan di port yang ditentukan (default: `8000`).

### 2. Alur Otentikasi (JWT)

- **Login**: Kirim `email` dan `password` ke endpoint `POST /auth/login`.
- **Respons Login Sukses**: Jika berhasil, API akan mengembalikan token JWT.
    ```json
    {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
    ```
- **Mengakses Endpoint Terproteksi**: Untuk setiap permintaan ke endpoint yang memerlukan otentikasi, sertakan token tersebut di header `Authorization` dengan skema `Bearer`.
    ```
    Authorization: Bearer <token_jwt_anda>
    ```
- **Token Kedaluwarsa/Tidak Valid**: Jika token tidak valid atau sudah kedaluwarsa, API akan mengembalikan status `401 Unauthorized` dengan respons error standar.

### 3. Format Respons API Standar

Untuk memastikan konsistensi, API ini menggunakan format respons yang terstandarisasi.

#### **Respons Sukses**

- **Untuk pengambilan data (GET)**, respons akan berisi data yang diminta dalam format JSON.
- **Untuk aksi (POST, PUT, DELETE)** yang hanya memerlukan konfirmasi, respons akan berisi pesan sukses:
    ```json
    // Contoh: DELETE /admin/users/{id}
    {
        "message": "Pengguna berhasil dihapus"
    }
    ```

#### **Respons Error (PENTING!)**

Semua respons error (status `400`, `401`, `403`, `404`, `500`, dll.) di seluruh API akan mengikuti struktur `shared.ErrorResponse` yang sama. Ini membuat penanganan error di sisi frontend menjadi lebih mudah dan dapat diprediksi.

**Struktur Error Standar:**
```json
{
    "error": "Pesan error yang jelas dalam Bahasa Indonesia."
}
```

**Contoh Kasus:**

- **Input Tidak Valid (400 Bad Request):**
    ```json
    {
        "error": "Email tidak boleh kosong"
    }
    ```
- **Tidak Terotentikasi (401 Unauthorized):**
    ```json
    {
        "error": "Token JWT tidak valid atau tidak ditemukan"
    }
    ```
- **Data Tidak Ditemukan (404 Not Found):**
    ```json
    {
        "error": "Pengguna tidak ditemukan"
    }
    ```

### 4. Dokumentasi API (Swagger)

Dokumentasi lengkap untuk semua endpoint API tersedia melalui Swagger.

- **URL**: `http://localhost:8000/swagger/index.html` (setelah server dijalankan).

Swagger menyediakan detail tentang setiap endpoint, termasuk:
- HTTP Method dan URL.
- Parameter yang dibutuhkan (path, query, body).
- Contoh *request body*.
- Struktur respons untuk sukses dan error.

**Selalu rujuk ke dokumentasi Swagger sebagai sumber kebenaran utama saat melakukan integrasi.**

---

## Arsitektur Proyek

Proyek ini menggunakan **Clean Architecture** untuk memastikan kode yang bersih, mudah ditest, dan mudah dipelihara. Struktur folder diorganisir berdasarkan layer tanggung jawab, bukan berdasarkan jenis file.

### Struktur Folder

```
backend-go/
├── cmd/
│   └── server/
│       └── main.go                    # Entry point aplikasi
├── internal/                          # Kode aplikasi private
│   ├── domain/                        # 🎯 CORE BUSINESS LOGIC
│   │   ├── entities/                  # Business entities/models
│   │   ├── repositories/              # Interface contracts untuk data access
│   │   └── usecases/                  # Business use cases
│   ├── infrastructure/                # 🔧 EXTERNAL CONCERNS
│   │   ├── http/                      # HTTP transport layer
│   │   │   ├── handlers/              # HTTP request handlers
│   │   │   ├── routes/                # Route definitions
│   │   │   └── middleware/            # HTTP middleware
│   │   ├── database/                  # Database layer
│   │   │   └── repositories/          # Repository implementations (GORM)
│   │   └── external/                  # Third-party services integration
│   └── application/                   # 🎮 APPLICATION LAYER
│       └── usecases/                  # Application use cases
├── pkg/                              # Public libraries/utilities
├── configs/                          # Configuration files
├── docs/                             # Documentation (Swagger)
├── uploads/                          # File uploads
└── logs/                             # Application logs
```

### Penjelasan Layer

#### 1. **Domain Layer** (`internal/domain/`)
- **Entities**: Struct data dan business rules yang tidak bergantung pada teknologi eksternal
- **Repositories**: Interface yang mendefinisikan kontrak untuk data access
- **Usecases**: Business logic murni tanpa ketergantungan pada framework

#### 2. **Infrastructure Layer** (`internal/infrastructure/`)
- **HTTP**: Semua yang berhubungan dengan transport HTTP (handlers, routes, middleware)
- **Database**: Implementasi repository menggunakan GORM
- **External**: Integrasi dengan service eksternal (payment gateway, notification, dll)

#### 3. **Application Layer** (`internal/application/`)
- **Usecases**: Orchestration layer yang menggabungkan domain logic dengan infrastructure

### Keuntungan Clean Architecture

1. **Independence**: Business logic tidak bergantung pada framework atau database
2. **Testability**: Mudah untuk membuat unit test karena dependency injection
3. **Maintainability**: Kode lebih mudah dipelihara dan dikembangkan
4. **Scalability**: Mudah menambah fitur baru tanpa merusak yang sudah ada
5. **Flexibility**: Mudah mengganti database atau framework tanpa mengubah business logic

### Dependency Flow

```
Infrastructure → Application → Domain
```

- **Domain** tidak bergantung pada layer lain
- **Application** hanya bergantung pada Domain
- **Infrastructure** bergantung pada Application dan Domain

### Contoh Implementasi

```go
// Domain Entity
type User struct {
    ID       uint   `json:"id"`
    Username string `json:"username"`
    Email    string `json:"email"`
}

// Domain Repository Interface
type UserRepository interface {
    GetByID(id uint) (*User, error)
    Create(user *User) error
}

// Infrastructure Repository Implementation
type userRepository struct {
    db *gorm.DB
}

func (r *userRepository) GetByID(id uint) (*User, error) {
    // GORM implementation
}

// Application Usecase
type UserUsecase struct {
    userRepo UserRepository
}

func (u *UserUsecase) GetUser(id uint) (*User, error) {
    return u.userRepo.GetByID(id)
}

// Infrastructure HTTP Handler
func GetUserHandler(userUsecase *UserUsecase) echo.HandlerFunc {
    return func(c echo.Context) error {
        // HTTP handling logic
    }
}
```
