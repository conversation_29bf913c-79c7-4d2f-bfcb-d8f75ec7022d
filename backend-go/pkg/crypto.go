package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"log"
	"os"
)

var encryptionKey []byte

// InitCrypto initializes the encryption key from environment variables.
// It must be called once at application startup after loading the .env file.
func InitCrypto() error {
	keyStr := os.Getenv("ENCRYPTION_KEY")
	if keyStr == "" {
		return errors.New("FATAL: ENCRYPTION_KEY is not set in .env file")
	}
	if len(keyStr) != 32 {
		return fmt.Errorf("FATAL: ENCRYPTION_KEY must be 32 characters long, but got %d", len(keyStr))
	}
	encryptionKey = []byte(keyStr)
	log.Println("✅ Encryption key loaded successfully.")
	return nil
}

// Encrypt mengenkripsi plaintext menggunakan AES-GCM.
// Encrypt encrypts a plaintext string using AES-GCM.
func Encrypt(plaintext string) (string, error) {
	if len(encryptionKey) == 0 {
		return "", errors.New("encryption key is not initialized. Call InitCrypto() first")
	}

	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher block: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return hex.EncodeToString(ciphertext), nil
}

// Decrypt mendekripsi ciphertext menggunakan AES-GCM.
// Decrypt decrypts a ciphertext hex string using AES-GCM.
func Decrypt(ciphertextHex string) (string, error) {
	if len(encryptionKey) == 0 {
		return "", errors.New("encryption key is not initialized. Call InitCrypto() first")
	}

	ciphertext, err := hex.DecodeString(ciphertextHex)
	if err != nil {
		return "", fmt.Errorf("failed to decode hex string: %w. The token might be corrupted or unencrypted", err)
	}

	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher block: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	if len(ciphertext) < gcm.NonceSize() {
		return "", errors.New("ciphertext is too short. The token might be corrupted or unencrypted")
	}

	nonce, ciphertext := ciphertext[:gcm.NonceSize()], ciphertext[gcm.NonceSize():]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt token: %w. The token might be corrupted or wrong key is used", err)
	}

	return string(plaintext), nil
}
