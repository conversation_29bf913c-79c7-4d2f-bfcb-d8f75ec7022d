package utils

import (
	"log"
	"sync"
	"time"

	"vpn-shop/backend-go/internal/infrastructure/database"
	"vpn-shop/backend-go/internal/domain/entities/admin"
)

var (
	currentSettings *admin.AdminSetting
	settingsMutex   sync.RWMutex
	lastFetched     time.Time
)

const cacheDuration = 1 * time.Minute // Cache settings for 1 minute

// GetSettings retrieves the current admin settings, using a cache to reduce db calls.
func GetSettings() (*admin.AdminSetting, error) {
	settingsMutex.RLock()
	// If cache is valid, return cached settings
	if currentSettings != nil && time.Since(lastFetched) < cacheDuration {
		defer settingsMutex.RUnlock()
		return currentSettings, nil
	}
	settingsMutex.RUnlock()

	// Cache is invalid or empty, need to fetch from DB
	settingsMutex.Lock()
	defer settingsMutex.Unlock()

	// Double-check if another goroutine fetched the settings while we were waiting for the lock
	if currentSettings != nil && time.Since(lastFetched) < cacheDuration {
		return currentSettings, nil
	}

	log.Println("Fetching admin settings from database...")
	var settings admin.AdminSetting
	if err := db.DB.First(&settings).Error; err != nil {
		log.Printf("Error fetching admin settings: %v. Returning last known settings or nil.", err)
		// Return the last known settings if available, even if expired
		if currentSettings != nil {
			return currentSettings, nil
		}
		return nil, err
	}

	currentSettings = &settings
	lastFetched = time.Now()

	return currentSettings, nil
}
