package utils

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/go-playground/validator/v10"
)

// CustomValidator wraps the go-playground/validator.
type CustomValidator struct {
	validator *validator.Validate
}

// Validate implements the echo.Validator interface.
func (cv *CustomValidator) Validate(i interface{}) error {
	if err := cv.validator.Struct(i); err != nil {
		return err
	}
	return nil
}

// NewValidator creates and returns a new validator instance with custom validations registered.
func NewValidator() *CustomValidator {
	validate := validator.New()

	// Custom validation for alphanumeric characters and dashes.
	_ = validate.RegisterValidation("alphanumdash", func(fl validator.FieldLevel) bool {
		// Regex to allow alphanumeric characters and dashes
		re := regexp.MustCompile(`^[a-zA-Z0-9-]+$`)
		return re.MatchString(fl.Field().String())
	})

	// Custom validation to ensure no spaces.
	_ = validate.RegisterValidation("is-no-spaces", func(fl validator.FieldLevel) bool {
		return !strings.Contains(fl.Field().String(), " ")
	})

	// Custom validation to ensure username does not start or end with a hyphen.
	_ = validate.RegisterValidation("no-edge-hyphens", func(fl validator.FieldLevel) bool {
		s := fl.Field().String()
		return !strings.HasPrefix(s, "-") && !strings.HasSuffix(s, "-")
	})

	// Custom validation for minimum length of alphanumeric characters (hyphens are not counted).
	_ = validate.RegisterValidation("min-alphanum-len", func(fl validator.FieldLevel) bool {
		param := fl.Param()
		minLength, err := strconv.Atoi(param)
		if err != nil {
			return false // Should not happen with correct usage
		}
		s := fl.Field().String()
		s = strings.ReplaceAll(s, "-", "")
		return len(s) >= minLength
	})

	return &CustomValidator{validator: validate}
}

// FormatValidationError formats the validation errors from go-playground/validator
// into a single, user-friendly string in Bahasa Indonesia.
func FormatValidationError(err error) string {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		var errorMessages []string
		for _, e := range validationErrors {
			// You can customize messages based on the 'tag'
			switch e.Tag() {
			case "required":
				errorMessages = append(errorMessages, fmt.Sprintf("%s tidak boleh kosong", e.Field()))
			case "email":
				errorMessages = append(errorMessages, "Format email tidak valid")
			case "min":
				errorMessages = append(errorMessages, fmt.Sprintf("%s harus memiliki minimal %s karakter", e.Field(), e.Param()))
			case "gt":
				errorMessages = append(errorMessages, fmt.Sprintf("%s harus lebih besar dari %s", e.Field(), e.Param()))
			case "oneof":
				errorMessages = append(errorMessages, fmt.Sprintf("%s harus salah satu dari: %s", e.Field(), strings.Replace(e.Param(), " ", ", ", -1)))
			case "is-no-spaces":
				errorMessages = append(errorMessages, fmt.Sprintf("%s tidak boleh mengandung spasi", e.Field()))
			case "alphanumdash":
				errorMessages = append(errorMessages, fmt.Sprintf("%s hanya boleh berisi huruf, angka, dan tanda hubung (-)", e.Field()))
			case "no-edge-hyphens":
				errorMessages = append(errorMessages, fmt.Sprintf("%s tidak boleh diawali atau diakhiri dengan tanda hubung (-)", e.Field()))
			case "min-alphanum-len":
				errorMessages = append(errorMessages, fmt.Sprintf("%s harus memiliki minimal %s karakter (tidak termasuk tanda hubung)", e.Field(), e.Param()))
			default:
				errorMessages = append(errorMessages, fmt.Sprintf("Kesalahan validasi pada field %s", e.Field()))
			}
		}
		return strings.Join(errorMessages, ", ")
	}
	return err.Error()
}
