# 🏗️ CLEAN ARCHITECTURE MIGRATION PLAN

## FASE 1: Reorgan<PERSON><PERSON> Folder (Tanpa Mengubah Kode)

### Langkah 1: Buat Struktur Baru
```bash
mkdir -p cmd/server
mkdir -p internal/domain/entities
mkdir -p internal/domain/repositories
mkdir -p internal/domain/usecases
mkdir -p internal/infrastructure/http/handlers
mkdir -p internal/infrastructure/http/routes
mkdir -p internal/infrastructure/http/middleware
mkdir -p internal/infrastructure/database/repositories
mkdir -p internal/infrastructure/external
mkdir -p internal/application/usecases
mkdir -p pkg/utils
mkdir -p configs
```

### Langkah 2: Pindahkan File (Copy dulu, jangan hapus)
```bash
# Entry point
cp main.go cmd/server/main.go

# Domain entities (models)
cp -r models/* internal/domain/entities/

# HTTP layer
cp -r handlers/* internal/infrastructure/http/handlers/
cp -r api/* internal/infrastructure/http/routes/
cp -r middleware/* internal/infrastructure/http/middleware/

# Application layer
cp -r services/* internal/application/usecases/

# Infrastructure
cp -r db/* internal/infrastructure/database/
cp -r utils/* pkg/utils/
cp -r core/* configs/
```

### Langkah 3: Update Import Paths
Setelah file dipindah, update semua import paths:

**Dari:**
```go
import "vpn-shop/backend-go/handlers/user"
```

**Menjadi:**
```go
import "vpn-shop/backend-go/internal/infrastructure/http/handlers/user"
```

## FASE 2: Implementasi Repository Pattern

### Contoh Repository Interface
```go
// internal/domain/repositories/user_repository.go
package repositories

import (
    "vpn-shop/backend-go/internal/domain/entities"
)

type UserRepository interface {
    GetByID(id uint) (*entities.User, error)
    GetByUsername(username string) (*entities.User, error)
    Create(user *entities.User) error
    Update(user *entities.User) error
    Delete(id uint) error
}
```

### Implementasi Repository
```go
// internal/infrastructure/database/repositories/user_repository.go
package repositories

import (
    "gorm.io/gorm"
    "vpn-shop/backend-go/internal/domain/entities"
    "vpn-shop/backend-go/internal/domain/repositories"
)

type userRepository struct {
    db *gorm.DB
}

func NewUserRepository(db *gorm.DB) repositories.UserRepository {
    return &userRepository{db: db}
}

func (r *userRepository) GetByID(id uint) (*entities.User, error) {
    var user entities.User
    err := r.db.First(&user, id).Error
    return &user, err
}
```

## FASE 3: Dependency Injection

### Container Pattern
```go
// internal/infrastructure/container.go
package infrastructure

import (
    "gorm.io/gorm"
    "vpn-shop/backend-go/internal/domain/repositories"
    dbRepos "vpn-shop/backend-go/internal/infrastructure/database/repositories"
    "vpn-shop/backend-go/internal/application/usecases"
)

type Container struct {
    UserRepo    repositories.UserRepository
    UserUsecase usecases.UserUsecase
}

func NewContainer(db *gorm.DB) *Container {
    userRepo := dbRepos.NewUserRepository(db)
    userUsecase := usecases.NewUserUsecase(userRepo)
    
    return &Container{
        UserRepo:    userRepo,
        UserUsecase: userUsecase,
    }
}
```

## KEUNTUNGAN CLEAN ARCHITECTURE

1. **Testability**: Mudah untuk unit testing
2. **Independence**: Business logic tidak tergantung framework
3. **Maintainability**: Kode lebih mudah dipelihara
4. **Scalability**: Mudah untuk menambah fitur baru
5. **Flexibility**: Mudah mengganti database/framework

## TIMELINE MIGRASI

- **Minggu 1**: Fase 1 (Reorganisasi folder)
- **Minggu 2**: Fase 2 (Repository pattern)
- **Minggu 3**: Fase 3 (Dependency injection)
- **Minggu 4**: Testing & cleanup

## CATATAN PENTING

- Jangan hapus struktur lama sampai migrasi selesai
- Test setiap fase sebelum lanjut ke fase berikutnya
- Update dokumentasi API setelah migrasi
- Pastikan semua test masih passing
