// File: backend-go/config/config.go
package core

import (
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

type Config struct {
	ProjectName              string
	ServerHost               string
	ServerPort               string
	Database                 Database
	JWTSecretKey             string
	JWTAlgorithm             string
	AccessTokenExpireMinutes int
	FirstSuperuserName       string
	FirstSuperuserUsername   string
	FirstSuperuserPassword   string
	FirstSuperuserEmail      string
	BackendCORSOrigins       []string
	TelegramBotSecretAuth    string
	TelegramBotToken         string
}

type Database struct {
	Name     string
	Host     string
	Port     string
	User     string
	Password string
}

func LoadConfig() *Config {
	if err := godotenv.Load(); err != nil {
		log.Fatal("Error: .env file not found. Please create one.")
	}

	accessTokenExpireMinutes, err := strconv.Atoi(getEnvOrFatal("ACCESS_TOKEN_EXPIRE_MINUTES"))
	if err != nil {
		log.Fatalf("Invalid ACCESS_TOKEN_EXPIRE_MINUTES: %v", err)
	}

	return &Config{
		ProjectName: getEnvOrFatal("PROJECT_NAME"),
		ServerHost:  getEnvOrFatal("SERVER_HOST"),
		ServerPort:  getEnvOrFatal("SERVER_PORT"),
		Database: Database{
			Name:     getEnvOrFatal("DB_NAME"),
			Host:     getEnvOrFatal("DB_HOST"),
			Port:     getEnvOrFatal("DB_PORT"),
			User:     getEnvOrFatal("DB_USER"),
			Password: getEnvOrFatal("DB_PASSWORD"),
		},
		JWTSecretKey:             getEnvOrFatal("JWT_SECRET_KEY"),
		JWTAlgorithm:             getEnvOrFatal("ALGORITHM"),
		AccessTokenExpireMinutes: accessTokenExpireMinutes,
		FirstSuperuserName:       getEnvOrFatal("FIRST_SUPERUSER_NAME"),
		FirstSuperuserUsername:   getEnvOrFatal("FIRST_SUPERUSER_USERNAME"),
		FirstSuperuserPassword:   getEnvOrFatal("FIRST_SUPERUSER_PASSWORD"),
		FirstSuperuserEmail:      getEnvOrFatal("FIRST_SUPERUSER_EMAIL"),
		BackendCORSOrigins:       strings.Split(getEnvOrFatal("BACKEND_CORS_ORIGINS"), ","),
		TelegramBotSecretAuth:    getEnvOrFatal("TELEGRAM_BOT_SECRET_AUTH"),
		TelegramBotToken:         getEnvOrFatal("TELEGRAM_BOT_TOKEN"),
	}
}

func getEnvOrFatal(key string) string {
	value, exists := os.LookupEnv(key)
	if !exists {
		log.Fatalf("Error: Environment variable %s not set.", key)
	}
	return value
}
