package db

import (
	"errors"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"vpn-shop/backend-go/configs"
	"vpn-shop/backend-go/internal/domain/entities/account"
	"vpn-shop/backend-go/internal/domain/entities/admin"
	"vpn-shop/backend-go/internal/domain/entities/announcement"
	"vpn-shop/backend-go/internal/domain/entities/notification"
	"vpn-shop/backend-go/internal/domain/entities/payment"
	"vpn-shop/backend-go/internal/domain/entities/server"
	"vpn-shop/backend-go/internal/domain/entities/user"

	"golang.org/x/crypto/bcrypt"
)

var DB *gorm.DB

func Init(cfg *core.Config) {
	var err error

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=Asia/Jakarta",
		cfg.Database.Host, cfg.Database.User, cfg.Database.Password, cfg.Database.Name, cfg.Database.Port)

	DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		NowFunc: func() time.Time {
			jakartaTime, _ := time.LoadLocation("Asia/Jakarta")
			return time.Now().In(jakartaTime)
		},
		Logger: logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			logger.Config{
				SlowThreshold:             time.Second,
				LogLevel:                  logger.Warn,
				IgnoreRecordNotFoundError: true,
				Colorful:                  true,
			},
		),
	})

	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	log.Println("Successfully connected to PostgreSQL database!")

	fmt.Println("Database connection established.")

	// Auto-migrate the schema
	err = DB.AutoMigrate(&user.User{}, &user.Role{}, &server.Server{}, &account.AccountTrojan{}, &account.AccountVmess{}, &account.AccountVless{}, &account.AccountSsh{}, &payment.Transaction{}, &payment.OrderItem{}, &payment.BillingHourlyHistory{}, &admin.AdminSetting{}, &announcement.Announcement{}, &notification.Notification{})

	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}
	log.Println("Database migrated successfully.")
}

// CreateInitialData creates roles and the first superuser if they don't exist.
func CreateInitialData(db *gorm.DB, cfg *core.Config) {
	// 1. Create Roles
	roles := []user.Role{{Name: "admin"}, {Name: "reseller"}, {Name: "member"}}
	for _, role := range roles {
		if err := db.First(&user.Role{}, "name = ?", role.Name).Error; errors.Is(err, gorm.ErrRecordNotFound) {
			db.Create(&role)
		}
	}

	// 2. Create First Superuser
	var superuserCheck user.User
	if err := db.First(&superuserCheck, "email = ?", cfg.FirstSuperuserEmail).Error; errors.Is(err, gorm.ErrRecordNotFound) {
		var adminRole user.Role
		if err := db.First(&adminRole, "name = ?", "admin").Error; err != nil {
			log.Fatalf("FATAL: 'admin' role not found. Seeding failed.")
			return
		}

		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(cfg.FirstSuperuserPassword), bcrypt.DefaultCost)
		if err != nil {
			log.Fatalf("FATAL: Failed to hash superuser password: %v", err)
			return
		}

		firstSuperuser := user.User{
			Name:     cfg.FirstSuperuserName,
			Username: cfg.FirstSuperuserUsername, // Tipe data string
			Email:    &cfg.FirstSuperuserEmail,   // Tipe data *string
			Password: string(hashedPassword),
			Roles:    []*user.Role{&adminRole},
		}

		if err := db.Create(&firstSuperuser).Error; err != nil {
			log.Printf("Could not create superuser: %v", err)
		} else {
			log.Println("First superuser created successfully.")
		}
	} else {
		log.Println("Superuser already exists. Skipping creation.")
	}

	// 3. Create Initial Admin Settings
	var setting admin.AdminSetting
	if err := db.First(&setting).Error; errors.Is(err, gorm.ErrRecordNotFound) {
		log.Println("No admin settings found. Seeding from .env file...")

		expiredMinutes, err := strconv.Atoi(os.Getenv("ACCOUNT_EXPIRATION_MINUTES"))
		if err != nil {
			log.Printf("Warning: Invalid or missing ACCOUNT_EXPIRATION_MINUTES, using default 90. Error: %v", err)
			expiredMinutes = 90
		}

		hourlyInterval, err := strconv.Atoi(os.Getenv("HOURLY_BILLING_INTERVAL_MINUTES"))
		if err != nil {
			log.Printf("Warning: Invalid or missing HOURLY_BILLING_INTERVAL_MINUTES, using default 1. Error: %v", err)
			hourlyInterval = 1
		}

		minSaldo, err := strconv.ParseInt(os.Getenv("MINIMAL_SALDO"), 10, 64)
		if err != nil {
			log.Printf("Warning: Invalid or missing MINIMAL_SALDO, using default 5000. Error: %v", err)
			minSaldo = 5000
		}

		dailyMtTime := os.Getenv("DAILY_MAINTENANCE_TIME")
		if dailyMtTime == "" {
			dailyMtTime = "00:00"
		}

		minTopUp, err := strconv.ParseInt(os.Getenv("MINIMAL_TOPUP"), 10, 64)
		if err != nil {
			log.Printf("Warning: Invalid or missing MINIMAL_TOPUP, using default 10000. Error: %v", err)
			minTopUp = 10000
		}

		minTopUpReseller, err := strconv.ParseInt(os.Getenv("MINIMAL_TOPUP_RESELLER"), 10, 64)
		if err != nil {
			log.Printf("Warning: Invalid or missing MINIMAL_TOPUP_RESELLER, using default 50000. Error: %v", err)
			minTopUpReseller = 50000
		}

		initialSettings := admin.AdminSetting{
			ExpiredMinutes:        expiredMinutes,
			DailyMtTime:           dailyMtTime,
			HourlyBillingInterval: hourlyInterval,
			MinSaldo:              minSaldo,
			MinTopUp:              minTopUp,
			MinTopUpReseller:      minTopUpReseller,
		}

		if err := db.Create(&initialSettings).Error; err != nil {
			log.Printf("Could not create initial admin settings: %v", err)
		} else {
			log.Println("Initial admin settings created successfully.")
		}
	} else {
		log.Println("Admin settings already exist. Skipping creation.")
	}
}

// Seed creates initial data in the database, such as roles and a superuser.
func Seed(cfg *core.Config) {
	// 1. Seed Roles
	var adminRole user.Role
	if err := DB.Where("name = ?", "admin").First(&adminRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			adminRole = user.Role{Name: "admin", Description: &[]string{"Administrator role"}[0]}
			if err := DB.Create(&adminRole).Error; err != nil {
				log.Fatalf("Failed to seed admin role: %v", err)
			}
			fmt.Println("Admin role seeded.")
		}
	}

	var userRole user.Role
	if err := DB.Where("name = ?", "user").First(&userRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			userRole = user.Role{Name: "user", Description: &[]string{"Default user role"}[0]}
			if err := DB.Create(&userRole).Error; err != nil {
				log.Fatalf("Failed to seed user role: %v", err)
			}
			fmt.Println("User role seeded.")
		}
	}

	var resellerRole user.Role
	if err := DB.Where("name = ?", "reseller").First(&resellerRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			resellerRole = user.Role{Name: "reseller", Description: &[]string{"Reseller role"}[0]}
			if err := DB.Create(&resellerRole).Error; err != nil {
				log.Fatalf("Failed to seed reseller role: %v", err)
			}
			fmt.Println("Reseller role seeded.")
		}
	}

	// 2. Seed Superuser
	var superuser user.User
	if err := DB.Where("email = ?", cfg.FirstSuperuserEmail).First(&superuser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte(cfg.FirstSuperuserPassword), bcrypt.DefaultCost)
			if err != nil {
				log.Fatalf("Failed to hash superuser password: %v", err)
			}

			superuser = user.User{
				Name:     cfg.FirstSuperuserName,
				Username: cfg.FirstSuperuserUsername,
				Email:    &cfg.FirstSuperuserEmail,
				Password: string(hashedPassword),
				Roles:    []*user.Role{&adminRole},
			}

			if err := DB.Create(&superuser).Error; err != nil {
				log.Fatalf("Failed to create superuser: %v", err)
			}
			fmt.Println("Superuser created successfully.")
		}
	}
}
