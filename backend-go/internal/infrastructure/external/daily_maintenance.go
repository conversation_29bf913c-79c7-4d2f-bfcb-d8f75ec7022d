package tasks

import (
	"log"
	"time"
	"vpn-shop/backend-go/internal/domain/entities/account"
	"vpn-shop/backend-go/internal/domain/entities/server"
	"vpn-shop/backend-go/internal/domain/entities/user"

	"gorm.io/gorm"
)

// RunDailyMaintenance menjalankan semua tugas pemeliharaan harian.
// Saat ini, tugas ini mencakup:
// 1. Mereset kuota trial pengguna.
// 2. Mereset penghitung hapus per jam pengguna.
// 3. Mengecek dan mengexpire akun yang sudah kadaluwarsa.
func RunDailyMaintenance(db *gorm.DB, logger *log.Logger) {
	logger.Println("Memulai tugas pemeliharaan harian...")

	// --- Tugas 1: Reset Kuota Trial ---
	logger.Println("Menjalankan sub-tugas: ResetTrialUsers")
	resultTrial := db.Model(&user.User{}).Where("trial > ?", 0).Update("trial", 0)
	if resultTrial.Error != nil {
		logger.Printf("Error saat mereset kuota trial: %v\n", resultTrial.Error)
	} else if resultTrial.RowsAffected > 0 {
		logger.Printf("Berhasil mereset kuota trial untuk %d pengguna.\n", resultTrial.RowsAffected)
	} else {
		logger.Println("Tidak ada kuota trial pengguna yang perlu direset.")
	}

	// --- Tugas 2: Reset Penghitung Hapus Per Jam ---
	logger.Println("Menjalankan sub-tugas: ResetDeleteHourlyCount")
	resultDeleteCount := db.Model(&user.User{}).Where("delete_hourly_count > ?", 0).Update("delete_hourly_count", 0)
	if resultDeleteCount.Error != nil {
		logger.Printf("Error saat mereset delete_hourly_count: %v\n", resultDeleteCount.Error)
	} else if resultDeleteCount.RowsAffected > 0 {
		logger.Printf("Berhasil mereset delete_hourly_count untuk %d pengguna.\n", resultDeleteCount.RowsAffected)
	} else {
		logger.Println("Tidak ada delete_hourly_count yang perlu direset.")
	}

	// --- Tugas 3: Cek dan Expire Akun yang Kadaluwarsa ---
	logger.Println("Menjalankan sub-tugas: CheckAndExpireAccounts")
	checkAndExpireAccounts(db, logger)

	logger.Println("Tugas pemeliharaan harian selesai.")
}

// checkAndExpireAccounts menemukan akun yang telah melewati tanggal kedaluwarsa,
// memperbarui statusnya menjadi "expired", dan mengurangi slot server.
func checkAndExpireAccounts(db *gorm.DB, logger *log.Logger) {
	now := time.Now()
	logger.Println("Memulai pengecekan akun yang expired...")

	// Map untuk tracking server yang perlu dikurangi slot-nya
	serverSlotReductions := make(map[string]int)

	// --- Handle AccountTrojan ---
	var trojanAccounts []account.AccountTrojan
	if err := db.Where("status = ? AND expired < ?", "active", now).Find(&trojanAccounts).Error; err != nil {
		logger.Printf("Error mencari akun Trojan yang expired: %v", err)
	} else if len(trojanAccounts) > 0 {
		logger.Printf("Menemukan %d akun Trojan untuk di-expired:", len(trojanAccounts))
		var ids []uint
		for _, acc := range trojanAccounts {
			ids = append(ids, acc.AccountID)
			serverSlotReductions[acc.KodeServer]++
			logger.Printf("  - Username: %s, Tipe: %s, Domain: %s, Server: %s", acc.Username, acc.SubscriptionType, acc.Domain, acc.KodeServer)
		}
		if err := db.Model(&account.AccountTrojan{}).Where("id IN ?", ids).Update("status", "expired").Error; err != nil {
			logger.Printf("Error saat memperbarui akun Trojan: %v", err)
		} else {
			logger.Printf("Berhasil memperbarui %d akun Trojan.", len(trojanAccounts))
		}
	}

	// --- Handle AccountVmess ---
	var vmessAccounts []account.AccountVmess
	if err := db.Where("status = ? AND expired < ?", "active", now).Find(&vmessAccounts).Error; err != nil {
		logger.Printf("Error mencari akun Vmess yang expired: %v", err)
	} else if len(vmessAccounts) > 0 {
		logger.Printf("Menemukan %d akun Vmess untuk di-expired:", len(vmessAccounts))
		var ids []uint
		for _, acc := range vmessAccounts {
			ids = append(ids, acc.AccountID)
			serverSlotReductions[acc.KodeServer]++
			logger.Printf("  - Username: %s, Tipe: %s, Domain: %s, Server: %s", acc.Username, acc.SubscriptionType, acc.Domain, acc.KodeServer)
		}
		if err := db.Model(&account.AccountVmess{}).Where("id IN ?", ids).Update("status", "expired").Error; err != nil {
			logger.Printf("Error saat memperbarui akun Vmess: %v", err)
		} else {
			logger.Printf("Berhasil memperbarui %d akun Vmess.", len(vmessAccounts))
		}
	}

	// --- Handle AccountVless ---
	var vlessAccounts []account.AccountVless
	if err := db.Where("status = ? AND expired < ?", "active", now).Find(&vlessAccounts).Error; err != nil {
		logger.Printf("Error mencari akun Vless yang expired: %v", err)
	} else if len(vlessAccounts) > 0 {
		logger.Printf("Menemukan %d akun Vless untuk di-expired:", len(vlessAccounts))
		var ids []uint
		for _, acc := range vlessAccounts {
			ids = append(ids, acc.AccountID)
			serverSlotReductions[acc.KodeServer]++
			logger.Printf("  - Username: %s, Tipe: %s, Domain: %s, Server: %s", acc.Username, acc.SubscriptionType, acc.Domain, acc.KodeServer)
		}
		if err := db.Model(&account.AccountVless{}).Where("id IN ?", ids).Update("status", "expired").Error; err != nil {
			logger.Printf("Error saat memperbarui akun Vless: %v", err)
		} else {
			logger.Printf("Berhasil memperbarui %d akun Vless.", len(vlessAccounts))
		}
	}

	// --- Handle AccountSsh ---
	var sshAccounts []account.AccountSsh
	if err := db.Where("status = ? AND expired < ?", "active", now).Find(&sshAccounts).Error; err != nil {
		logger.Printf("Error mencari akun Ssh yang expired: %v", err)
	} else if len(sshAccounts) > 0 {
		logger.Printf("Menemukan %d akun Ssh untuk di-expired:", len(sshAccounts))
		var ids []uint
		for _, acc := range sshAccounts {
			ids = append(ids, acc.AccountID)
			serverSlotReductions[acc.KodeServer]++
			logger.Printf("  - Username: %s, Tipe: %s, Domain: %s, Server: %s", acc.Username, acc.SubscriptionType, acc.Domain, acc.KodeServer)
		}
		if err := db.Model(&account.AccountSsh{}).Where("id IN ?", ids).Update("status", "expired").Error; err != nil {
			logger.Printf("Error saat memperbarui akun Ssh: %v", err)
		} else {
			logger.Printf("Berhasil memperbarui %d akun Ssh.", len(sshAccounts))
		}
	}

	// --- Update Server Statistics ---
	logger.Println("Memperbarui statistik server...")
	for serverCode, reductionCount := range serverSlotReductions {
		if err := db.Model(&server.Server{}).Where("kode = ?", serverCode).Updates(map[string]interface{}{
			"slot_terpakai": gorm.Expr("slot_terpakai - ?", reductionCount),
			"total_user":    gorm.Expr("total_user - ?", reductionCount),
		}).Error; err != nil {
			logger.Printf("Error saat mengurangi slot server %s: %v", serverCode, err)
		} else {
			logger.Printf("Berhasil mengurangi %d slot untuk server %s", reductionCount, serverCode)
		}
	}

	logger.Println("Selesai pengecekan akun yang expired.")
}
