package server

import (
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net"
	"net/http"
	"strconv"
	"strings"
	"time"
	"vpn-shop/backend-go/internal/infrastructure/database"
	serverModel "vpn-shop/backend-go/internal/domain/entities/server"
	"vpn-shop/backend-go/internal/domain/entities/shared"
	"vpn-shop/backend-go/pkg"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// generateRandomCode generates a random code of 6 alphanumeric characters
func generateRandomCode() string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz"
	code := make([]byte, 6)
	for i := range code {
		code[i] = charset[rand.Intn(len(charset))]
	}
	return string(code)
}

// ensureUniqueCode ensures that the generated code is unique
func ensureUniqueCode() string {
	for {
		code := generateRandomCode()
		var existingServer serverModel.Server
		if err := db.DB.Where("kode = ?", code).First(&existingServer).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return code // Code is unique
			}
		}
		// If we get here, code already exists, try again
	}
}

// SwaggerServerResponse is used to explicitly define the Server response structure for Swagger
type SwaggerServerResponse struct {
	ServerID      uint      `json:"server_id"`
	Nama          string    `json:"nama"`
	Kode          string    `json:"kode"`
	Domain        string    `json:"domain"`
	Token         string    `json:"token"`
	Negara        string    `json:"negara"`
	NamaIsp       string    `json:"nama_isp"`
	HargaMember   int64     `json:"harga_member"`
	HargaReseller int64     `json:"harga_reseller"`
	SSH           string    `json:"ssh"`
	Trojan        string    `json:"trojan"`
	Vmess         string    `json:"vmess"`
	Vless         string    `json:"vless"`
	SlotServer    int       `json:"slot_server"`
	SlotTerpakai  int       `json:"slot_terpakai"`
	TotalUser     int       `json:"total_user"`
	MaxDevice     int       `json:"max_device"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// SwaggerServerListResponse is used to explicitly define the Server list response for Swagger
type SwaggerServerListResponse struct {
	Servers []SwaggerServerResponse `json:"servers"`
}

var validate = validator.New()

// CreateServer creates a new VPS serverModel.
// @Summary Create a new server
// @Description Create a new VPS server with detailed information
// @Tags server
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param server body serverModel.CreateServerRequest true "Server data"
// @Success 201 {object} SwaggerServerResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Failure 409 {object} shared.ErrorResponse "Kode already exists"
// @Failure 500 {object} shared.ErrorResponse
// @Router /servers [post]
func CreateServer(c echo.Context) error {
	var req serverModel.CreateServerRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	if err := validate.Struct(req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: utils.FormatValidationError(err)})
	}

	// If kode is empty or not provided, generate a random unique code
	if strings.TrimSpace(req.Kode) == "" {
		req.Kode = ensureUniqueCode()
	} else {
		// Check if server with the same kode already exists
		var existingServer serverModel.Server
		if err := db.DB.Where("kode = ?", req.Kode).First(&existingServer).Error; err == nil {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Server dengan kode tersebut sudah ada"})
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Printf("Kesalahan memeriksa server yang sudah ada: %v", err)
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Terjadi kesalahan pada server"})
		}
	}

	// Encrypt the token before saving
	encryptedToken, err := utils.Encrypt(req.Token)
	if err != nil {
		log.Printf("Gagal mengenkripsi token: %v", err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengamankan token"})
	}

	// Create new server
	server := serverModel.Server{
		Nama:          req.Nama,
		Kode:          req.Kode,
		Domain:        req.Domain,
		Token:         encryptedToken,
		Negara:        req.Negara,
		NamaIsp:       req.NamaIsp,
		HargaMember:   req.HargaMember,
		HargaReseller: req.HargaReseller,
		SSH:           req.SSH,
		Trojan:        req.Trojan,
		Vmess:         req.Vmess,
		Vless:         req.Vless,
		SlotServer:    req.SlotServer,
		SlotTerpakai:  req.SlotTerpakai,
		TotalUser:     req.TotalUser,
		MaxDevice:     req.MaxDevice,
	}

	if err := db.DB.Create(&server).Error; err != nil {
		log.Printf("Gagal membuat server: %v", err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat server"})
	}

	return c.JSON(http.StatusCreated, server.ToResponse())
}

// GetAllServers retrieves all VPS servers.
// @Summary Get all servers
// @Description Get a list of all VPS servers
// @Tags server
// @Security BearerAuth
// @Produce json
// @Success 200 {object} serverModel.PublicServerListResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /servers [get]
func GetAllServers(c echo.Context) error {
	var servers []serverModel.Server
	if err := db.DB.Find(&servers).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Tidak dapat mengambil data server"})
	}

	// Convert to public response to hide sensitive data
	publicServers := make([]serverModel.PublicServerResponse, len(servers))
	for i, s := range servers {
		publicServers[i] = s.ToPublicResponse()
	}

	return c.JSON(http.StatusOK, serverModel.PublicServerListResponse{Servers: publicServers})
}

// GetServerByID retrieves a specific VPS server by ID.
// @Summary Get a server by ID
// @Description Get detailed information about a specific VPS server
// @Tags server
// @Security BearerAuth
// @Produce json
// @Param id path int true "Server ID"
// @Success 200 {object} serverModel.PublicServerResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /servers/{id} [get]
func GetServerByID(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "ID Server tidak valid"})
	}

	var server serverModel.Server
	if dbErr := db.DB.First(&server, id).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Server tidak ditemukan"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Tidak dapat mengambil data server"})
	}

	// Convert to public response to hide sensitive data
	return c.JSON(http.StatusOK, server.ToPublicResponse())
}

// UpdateServer updates a VPS server by ID.
// @Summary Update a server
// @Description Update information for an existing VPS server
// @Tags server
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Server ID"
// @Param server body serverModel.UpdateServerRequest true "Server data to update"
// @Success 200 {object} SwaggerServerResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /servers/{id} [put]
func UpdateServer(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Invalid ServerID"})
	}

	var server serverModel.Server
	if dbErr := db.DB.First(&server, id).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Server not found"})
		}
		log.Printf("Error fetching server with ServerID %d: %v", id, dbErr)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Failed to fetch server"})
	}

	var req serverModel.UpdateServerRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	if err := validate.Struct(req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: utils.FormatValidationError(err)})
	}

	// Update fields if provided in request
	if req.Nama != nil {
		server.Nama = *req.Nama
	}
	if req.Domain != nil {
		server.Domain = *req.Domain
	}
	if req.Token != nil {
		encryptedToken, err := utils.Encrypt(*req.Token)
		if err != nil {
			log.Printf("Gagal mengenkripsi token: %v", err)
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengamankan token"})
		}
		server.Token = encryptedToken
	}
	if req.Negara != nil {
		server.Negara = *req.Negara
	}
	if req.NamaIsp != nil {
		server.NamaIsp = *req.NamaIsp
	}
	if req.HargaMember != nil {
		server.HargaMember = *req.HargaMember
	}
	if req.HargaReseller != nil {
		server.HargaReseller = *req.HargaReseller
	}
	if req.SSH != nil {
		server.SSH = req.SSH
	}
	if req.Trojan != nil {
		server.Trojan = req.Trojan
	}
	if req.Vmess != nil {
		server.Vmess = req.Vmess
	}
	if req.Vless != nil {
		server.Vless = req.Vless
	}
	if req.SlotServer != nil {
		server.SlotServer = *req.SlotServer
	}
	if req.SlotTerpakai != nil {
		server.SlotTerpakai = req.SlotTerpakai
	}
	// Total user field is managed by the system and should not be updated via PUT request
	if req.MaxDevice != nil {
		server.MaxDevice = *req.MaxDevice
	}

	if err := db.DB.Save(&server).Error; err != nil {
		log.Printf("Gagal memperbarui server dengan ID %d: %v", id, err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui server"})
	}

	return c.JSON(http.StatusOK, server.ToResponse())
}

// DeleteServer soft deletes a VPS server by ID.
// @Summary Delete a server
// @Description Soft delete a VPS server by ID
// @Tags server
// @Security BearerAuth
// @Produce json
// @Param id path int true "Server ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /servers/{id} [delete]
func DeleteServer(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "ID Server tidak valid"})
	}

	var server serverModel.Server
	if dbErr := db.DB.First(&server, id).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Server tidak ditemukan"})
		}
		log.Printf("Error fetching server with ServerID %d: %v", id, dbErr)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data server"})
	}

	if err := db.DB.Delete(&server).Error; err != nil {
		log.Printf("Gagal menghapus server dengan ID %d: %v", id, err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menghapus server"})
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Server berhasil dihapus"})
}

// MigrateFromLegacyServers migrates data from the legacy my_servers table.
// @Summary Migrate legacy server data
// @Description Migrate data from legacy my_servers table to the new server model
// @Tags server
// @Security BearerAuth
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 401 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /servers/migrate [post]
func MigrateFromLegacyServers(c echo.Context) error {
	// Define legacy server structure based on my_servers table
	type LegacyServer struct {
		ID            uint   `gorm:"primaryKey"`
		Nama          string `gorm:"type:varchar(255)"`
		Kode          string `gorm:"type:varchar(255);uniqueIndex"`
		Domain        string `gorm:"type:varchar(255)"`
		Password      string `gorm:"type:varchar(255)"`
		Negara        string `gorm:"type:varchar(255)"`
		IspName       string `gorm:"type:varchar(255);column:isp_name"`
		HargaReseller int64  `gorm:"type:decimal(15,0)"`
		HargaMember   int64  `gorm:"type:decimal(15,0)"`
		SSH           string `gorm:"type:varchar(255)"`
		Vmess         string `gorm:"type:varchar(255)"`
		Vless         string `gorm:"type:varchar(255)"`
		TrojanWs      string `gorm:"type:varchar(255);column:trojan_ws"`
		SlotServer    int    `gorm:"type:integer"`
		SlotTerpakai  int    `gorm:"type:integer"`
		TotalUser     int    `gorm:"type:double(8,0)"`
		MaxDevice     int    `gorm:"type:integer;default:1"`
	}

	// Configure the table name for legacy server
	db.DB.Table("my_servers")

	// Fetch all legacy servers
	var legacyServers []LegacyServer
	if err := db.DB.Table("my_servers").Find(&legacyServers).Error; err != nil {
		log.Printf("Gagal mengambil data server lama: %v", err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data server lama"})
	}

	// Migrate each legacy server to the new model
	for _, legacy := range legacyServers {
		// Check if server with this kode already exists in new table
		var existingServer serverModel.Server
		if err := db.DB.Where("kode = ?", legacy.Kode).First(&existingServer).Error; err == nil {
			// Server already migrated, skip
			continue
		}

		ssh := legacy.SSH
		trojan := legacy.TrojanWs
		vmess := legacy.Vmess
		vless := legacy.Vless
		slotTerpakai := legacy.SlotTerpakai
		totalUser := legacy.TotalUser

		// Create new server from legacy data
		server := serverModel.Server{
			Nama:          legacy.Nama,
			Kode:          legacy.Kode,
			Domain:        legacy.Domain,
			Token:         legacy.Password, // Use Password as Token
			Negara:        legacy.Negara,
			NamaIsp:       legacy.IspName,
			HargaMember:   legacy.HargaMember,
			HargaReseller: legacy.HargaReseller,
			SSH:           &ssh,
			Trojan:        &trojan,
			Vmess:         &vmess,
			Vless:         &vless,
			SlotServer:    legacy.SlotServer,
			SlotTerpakai:  &slotTerpakai,
			TotalUser:     &totalUser,
			MaxDevice:     legacy.MaxDevice,
		}

		if err := db.DB.Create(&server).Error; err != nil {
			log.Printf("Gagal memigrasi server lama %s: %v", legacy.Kode, err)
			// Continue with next server instead of failing the whole migration
			continue
		}
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Migrasi server lama berhasil"})
}

// TestServerToken tests the connection to a server using its token.
// @Summary Test server token
// @Description Tests the connection to a server by making a request to its admin API with the stored token.
// @Tags server
// @Security BearerAuth
// @Produce json
// @Param id path int true "Server ID"
// @Success 200 {object} map[string]interface{} "Successful response from the server"
// @Failure 400 {object} shared.ErrorResponse "Invalid ServerID"
// @Failure 401 {object} shared.ErrorResponse "Unauthorized"
// @Failure 403 {object} shared.ErrorResponse "Forbidden"
// @Failure 404 {object} shared.ErrorResponse "Server not found"
// @Failure 500 {object} shared.ErrorResponse "Internal server error or failed to connect to the server"
// @Router /servers/{id}/test-token [get]
func TestServerToken(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "ID Server tidak valid"})
	}

	var server serverModel.Server
	if dbErr := db.DB.First(&server, id).Error; dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Server tidak ditemukan"})
		}
		log.Printf("Error fetching server with ServerID %d: %v", id, dbErr)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data server"})
	}

	url := "https://" + server.Domain + "/api/admin"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Gagal membuat permintaan untuk server dengan ID %d: %v", id, err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat permintaan"})
	}

	// Decrypt the token before using it
	decryptedToken, decryptErr := utils.Decrypt(server.Token)
	if decryptErr != nil {
		log.Printf("Gagal mendekripsi token untuk server dengan ID %d: %v", id, decryptErr)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses token"})
	}

	// Set the authorization header
	req.Header.Set("Authorization", "Bearer "+decryptedToken)
	req.Header.Set("Accept", "application/json")

	// Execute the request
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Gagal terhubung ke server dengan ID %d (%s): %v", id, url, err)
		// Periksa apakah error adalah error jaringan dan apakah itu timeout
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal terhubung ke server " + server.Domain + ". Permintaan timeout"})
		}
		// Error umum untuk masalah koneksi lainnya, dengan pesan yang lebih ramah pengguna
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal terhubung ke server " + server.Domain + ". Pastikan domain dan koneksi benar."})
	}
	defer resp.Body.Close()

	// If the connection is successful (status 2xx), return a custom success message
	if resp.StatusCode >= http.StatusOK && resp.StatusCode < http.StatusMultipleChoices {
		return c.JSON(http.StatusOK, map[string]string{"message": "Uji koneksi berhasil ke " + server.NamaIsp})
	}

	// If the connection was not successful, proxy the original error response
	body, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		log.Printf("Gagal membaca respons dari server dengan ID %d: %v", id, readErr)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membaca respons dari server"})
	}

	// Return the original error response from the target server to the client
	return c.Blob(resp.StatusCode, resp.Header.Get("Content-Type"), body)
}

// TestNewServerToken tests a new server token without saving it to database.
// @Summary Test new server token
// @Description Tests the connection to a server using provided domain and token without saving to database.
// @Tags server
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body serverModel.TestTokenRequest true "Domain and token to test"
// @Success 200 {object} map[string]interface{} "Successful response from the server"
// @Failure 400 {object} shared.ErrorResponse "Invalid request data"
// @Failure 401 {object} shared.ErrorResponse "Unauthorized"
// @Failure 403 {object} shared.ErrorResponse "Forbidden"
// @Failure 500 {object} shared.ErrorResponse "Internal server error or failed to connect to the server"
// @Router /servers/test-token [post]
func TestNewServerToken(c echo.Context) error {
	var req serverModel.TestTokenRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	if err := validate.Struct(req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: utils.FormatValidationError(err)})
	}

	// Construct the URL with the provided domain and API path
	url := "https://" + req.Domain + req.ApiPath
	request, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Gagal membuat permintaan untuk domain %s: %v", req.Domain, err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat permintaan"})
	}

	// Set the authorization header with the provided token
	request.Header.Set("Authorization", "Bearer "+req.Token)
	request.Header.Set("Accept", "application/json")

	// Execute the request
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(request)
	if err != nil {
		log.Printf("Gagal terhubung ke server %s: %v", req.Domain, err)
		// Check if error is a network error and if it's a timeout
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal terhubung ke server " + req.Domain + ". Permintaan timeout"})
		}
		// General error for other connection issues, with a more user-friendly message
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal terhubung ke server " + req.Domain + ". Pastikan domain dan koneksi benar."})
	}
	defer resp.Body.Close()

	// Check for authentication errors
	if resp.StatusCode == http.StatusUnauthorized || resp.StatusCode == http.StatusForbidden {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Token tidak valid atau tidak memiliki izin"})
	}

	// If the connection is successful (status 2xx), return a custom success message
	if resp.StatusCode >= http.StatusOK && resp.StatusCode < http.StatusMultipleChoices {
		return c.JSON(http.StatusOK, map[string]string{"message": "Koneksi berhasil " + req.Domain})
	}

	// If the connection was not successful, return error with status code
	body, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		log.Printf("Gagal membaca respons dari server %s: %v", req.Domain, readErr)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membaca respons dari server"})
	}

	// Return error with status information
	maxLen := 100
	if len(body) < maxLen {
		maxLen = len(body)
	}
	return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: fmt.Sprintf("Koneksi ke server gagal. Status: %d. Respons: %s", resp.StatusCode, string(body)[:maxLen])})
}
