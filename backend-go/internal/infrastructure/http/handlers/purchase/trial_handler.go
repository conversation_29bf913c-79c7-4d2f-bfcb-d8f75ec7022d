package purchase

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm/clause"

	"github.com/go-playground/validator/v10"
	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"

	"vpn-shop/backend-go/internal/infrastructure/database"
	"vpn-shop/backend-go/internal/domain/entities/account"
	"vpn-shop/backend-go/internal/domain/entities/admin"
	"vpn-shop/backend-go/internal/domain/entities/payment"
	"vpn-shop/backend-go/internal/domain/entities/server"
	"vpn-shop/backend-go/internal/domain/entities/shared"
	"vpn-shop/backend-go/internal/domain/entities/user"
	"vpn-shop/backend-go/internal/application/usecases"
	"vpn-shop/backend-go/pkg"
)

// PurchaseTrialRequest defines the structure for a trial purchase request.
type PurchaseTrialRequest struct {
	KodeServer string `json:"kode_server" validate:"required"`
	Protocol   string `json:"protocol" validate:"required,oneof=trojan vmess vless"`
}

// PurchaseTrialAccount handles the logic for purchasing a new trial VPN account.
// @Summary Create a new trial account purchase record
// @Description Processes a new trial VPN account purchase. Limited to one per user. Username is auto-generated.
// @Tags Purchase
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param purchase body PurchaseTrialRequest true "Trial Purchase Data"
// @Success 201 {object} map[string]interface{}
// @Failure 400 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse "Jatah trial sudah habis"
// @Failure 404 {object} shared.ErrorResponse "Server tidak ditemukan"
// @Failure 409 {object} shared.ErrorResponse "Layanan tidak tersedia atau tidak dapat membuat username unik"
// @Failure 500 {object} shared.ErrorResponse
// @Router /purchase/trial [post]
func PurchaseTrialAccount(c echo.Context) error {
	// 1. Get User from JWT Token
	userToken := c.Get("user").(*jwt.Token)
	claims := userToken.Claims.(*jwt.MapClaims)
	userID := uint((*claims)["user_id"].(float64))

	// 2. Bind and Validate Request Body
	var req PurchaseTrialRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format request tidak valid"})
	}
	validate := validator.New()
	if err := validate.Struct(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: err.Error()})
	}

	// 3. Pre-flight checks and data preparation
	var serverData server.Server
	if err := db.DB.Where("kode = ?", req.KodeServer).First(&serverData).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Server tidak ditemukan"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mendapatkan detail server"})
	}

	// Check if the requested protocol is available on the server
	switch req.Protocol {
	case "trojan":
		if serverData.Trojan == nil || *serverData.Trojan == "" || *serverData.Trojan == "disable" {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Protokol (trojan) tidak aktif di server ini"})
		}
	case "vmess":
		if serverData.Vmess == nil || *serverData.Vmess == "" || *serverData.Vmess == "disable" {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Protokol (vmess) tidak aktif di server ini"})
		}
	case "vless":
		if serverData.Vless == nil || *serverData.Vless == "" || *serverData.Vless == "disable" {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Protokol (vless) tidak aktif di server ini"})
		}
	default:
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: fmt.Sprintf("Protokol tidak didukung: %s", req.Protocol)})
	}

	if serverData.Domain == "" || serverData.Token == "" {
		return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Konfigurasi API (domain/token) tidak tersedia di server ini"})
	}

	decryptedToken, err := utils.Decrypt(serverData.Token)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses token server"})
	}

	// Get admin settings for minimum balance
	var adminSettings admin.AdminSetting
	err = db.DB.First(&adminSettings).Error
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memuat pengaturan admin"})
	}

	// 4. Pre-flight Check: Verify external API connection
	err = services.CheckAPIConnection(serverData.Domain, decryptedToken)
	if err != nil {
		return c.JSON(http.StatusServiceUnavailable, shared.ErrorResponse{Error: fmt.Sprintf("API eksternal tidak dapat dijangkau: %v", err)})
	}

	// 5. Generate a unique username
	var marzbanUsername string
	const maxRetries = 5
	for i := 0; i < maxRetries; i++ {
		var randomPart string
		randomPart, err = utils.GenerateRandomString(8)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat bagian acak untuk username"})
		}
		tempUsername := "trial_" + randomPart

		// Check if username exists in local DB
		var localExists bool
		localExists, err = utils.IsUsernameExists(db.DB, tempUsername, req.KodeServer)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memeriksa username di database lokal: " + err.Error()})
		}
		if localExists {
			continue // Try again
		}

		// Check if username exists on Marzban server
		var marzbanUser *shared.MarzbanUserResponse
		marzbanUser, err = services.GetUser(serverData.Domain, decryptedToken, tempUsername)
		if err != nil {
			// An error occurred while checking Marzban, not just 'not found'
			return c.JSON(http.StatusServiceUnavailable, shared.ErrorResponse{Error: fmt.Sprintf("Gagal memeriksa username di API eksternal: %v", err)})
		}
		if marzbanUser != nil {
			// User exists on Marzban, conflict
			continue // Try again
		}

		// Username is unique in both local DB and Marzban
		marzbanUsername = tempUsername
		break
	}

	if marzbanUsername == "" {
		return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Tidak dapat membuat username unik setelah beberapa kali percobaan. Silakan coba lagi."})
	}

	uuid, err := utils.GenerateUUID()
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat kredensial akun"})
	}

	var newAccountData account.IAccount

	// 6. Database Transaction
	txErr := db.DB.Transaction(func(tx *gorm.DB) error {
		// Lock user data for update to prevent race conditions
		var userData user.User
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&userData, userID).Error; err != nil {
			return fmt.Errorf("pengguna tidak ditemukan")
		}

		// Check if user's balance meets the minimum requirement for a trial
		if userData.Saldo < adminSettings.MinSaldo {
			return fmt.Errorf("trial harus memiliki minimal saldo %s", utils.FormatRupiah(adminSettings.MinSaldo))
		}

		// Check if user has already used up their trial quota by comparing with their specific limit
		batasTrialUser := 5 // Default limit based on the gorm tag in the user model
		if userData.BatasTrial != nil {
			batasTrialUser = *userData.BatasTrial
		}

		if userData.Trial != nil && *userData.Trial >= batasTrialUser {
			return fmt.Errorf("jatah trial sudah digunakan")
		}
		if userData.Trial == nil {
			zero := 0
			userData.Trial = &zero
		}

		// --- All checks passed, proceed with creation ---

		tanggalBeli := time.Now()
		finalExpired := tanggalBeli.AddDate(0, 0, 1) // Trial for 1 day

		// Prepare payload for Marzban API
		var proxies map[string]interface{}
		switch req.Protocol {
		case "trojan":
			proxies = map[string]interface{}{
				"trojan": services.CreateTrojanProxySettings(uuid),
			}
		case "vmess":
			proxies = map[string]interface{}{
				"vmess": services.CreateVmessProxySettings(uuid),
			}
		case "vless":
			proxies = map[string]interface{}{
				"vless": services.CreateVlessProxySettings(uuid),
			}
		default:
			return fmt.Errorf("protokol tidak didukung untuk pembuatan proxy settings: %s", req.Protocol)
		}

		payload := services.MarzbanUserRequest{
			Username:               marzbanUsername,
			Note:                   "Akun Trial",
			Proxies:                proxies,
			Inbounds:               map[string][]string{req.Protocol: {}},
			Expire:                 finalExpired.Unix(),
			DataLimit:              2 * 1024 * 1024 * 1024, // 2 GB
			DataLimitResetStrategy: "no_reset",
			Status:                 "active",
		}

		// Call the external API WITHIN the transaction.
		// If this fails, the entire transaction will be rolled back automatically.
		if err := services.CreateUser(serverData.Domain, decryptedToken, payload); err != nil {
			return fmt.Errorf("gagal membuat pengguna di API eksternal: %w", err)
		}

		// --- API call successful, now create local records ---

		kodeAkun, err := utils.GenerateRandomString(10)
		if err != nil {
			return fmt.Errorf("gagal membuat kode akun: %w", err)
		}

		switch req.Protocol {
		case "trojan":
			newAccountData = &account.AccountTrojan{
				UserID:           strconv.Itoa(int(userID)),
				KodeServer:       req.KodeServer,
				KodeAkun:         kodeAkun,
				Domain:           serverData.Domain,
				Durasi:           "1 day",
				Username:         marzbanUsername,
				UUID:             uuid,
				Expired:          &finalExpired,
				TanggalBeli:      tanggalBeli,
				Status:           "active",
				SubscriptionType: "trial",
				Harga:            "0",
			}
		case "vmess":
			newAccountData = &account.AccountVmess{
				UserID:           strconv.Itoa(int(userID)),
				KodeServer:       req.KodeServer,
				KodeAkun:         kodeAkun,
				Domain:           serverData.Domain,
				Durasi:           "1 day",
				Username:         marzbanUsername,
				UUID:             uuid,
				Expired:          &finalExpired,
				TanggalBeli:      tanggalBeli,
				Status:           "active",
				SubscriptionType: "trial",
				Harga:            "0",
			}
		case "vless":
			newAccountData = &account.AccountVless{
				UserID:           strconv.Itoa(int(userID)),
				KodeServer:       req.KodeServer,
				KodeAkun:         kodeAkun,
				Domain:           serverData.Domain,
				Durasi:           "1 day",
				Username:         marzbanUsername,
				UUID:             uuid,
				Expired:          &finalExpired,
				TanggalBeli:      tanggalBeli,
				Status:           "active",
				SubscriptionType: "trial",
				Harga:            "0",
			}
		}

		if err := tx.Create(newAccountData).Error; err != nil {
			return err
		}

		// Create a transaction record for the trial
		accID := newAccountData.GetID()
		accType := req.Protocol
		transaction := payment.Transaction{
			InvoiceID:   fmt.Sprintf("INV-TRIAL-%d-%s", userID, kodeAkun),
			UserID:      userID,
			AccountID:   &accID,
			AccountType: &accType,
			Duration:    utils.StringToPointer("Trial"),
			Type:        payment.Trial,
			Description: fmt.Sprintf("Pengambilan akun trial %s (%s)", req.Protocol, marzbanUsername),
			Amount:      0,
			Status:      payment.Success,
		}
		if err := tx.Create(&transaction).Error; err != nil {
			return fmt.Errorf("gagal membuat catatan transaksi trial: %w", err)
		}

		// Increment user's trial count
		*userData.Trial++
		if err := tx.Save(&userData).Error; err != nil {
			return err
		}

		return nil // Commit transaction
	})

	if txErr != nil {
		// Check for specific, user-facing errors first
		if strings.Contains(txErr.Error(), "jatah trial sudah digunakan") {
			return c.JSON(http.StatusForbidden, shared.ErrorResponse{Error: "Anda sudah menggunakan jatah akun trial."})
		} else if strings.Contains(txErr.Error(), "Trial harus memiliki minimal saldo") {
			return c.JSON(http.StatusPaymentRequired, shared.ErrorResponse{Error: txErr.Error()})
		}
		// Generic error for other failures
		c.Logger().Errorf("Trial purchase failed: %v", txErr)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses permintaan trial: " + txErr.Error()})
	}

	// Create notification for successful trial creation
	if err := services.CreateTrialNotification(userID, marzbanUsername, req.KodeServer); err != nil {
		c.Logger().Warnf("Failed to create trial notification for user %d: %v", userID, err)
		// Don't fail the request if notification fails
	}

	// 8. Everything is successful! Create a clean response.
	// We explicitly define the response structure to avoid leaking unwanted fields like the empty server object.
	responseData := map[string]interface{}{
		"account_id":        newAccountData.GetID(),
		"user_id":           newAccountData.GetUserID(),
		"kode_server":       newAccountData.GetKodeServer(),
		"kode_akun":         newAccountData.GetKodeAkun(),
		"domain":            newAccountData.GetDomain(),
		"durasi":            newAccountData.GetDurasi(),
		"username":          newAccountData.GetUsername(),
		"uuid":              newAccountData.GetUUID(),
		"expired":           newAccountData.GetExpiredDate(),
		"tanggal_beli":      newAccountData.GetTanggalBeli(),
		"status":            newAccountData.GetStatus(),
		"subscription_type": newAccountData.GetSubscriptionType(),
		"harga":             newAccountData.GetHarga(),
		"created_at":        newAccountData.GetCreatedAt(),
		"updated_at":        newAccountData.GetUpdatedAt(),
	}

	return c.JSON(http.StatusCreated, map[string]interface{}{
		"message": "Akun trial berhasil dibuat.",
		"data":    responseData,
	})
}
