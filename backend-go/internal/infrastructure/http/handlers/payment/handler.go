package payment

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"vpn-shop/backend-go/internal/infrastructure/database"
	"vpn-shop/backend-go/internal/infrastructure/http/middleware"
	"vpn-shop/backend-go/internal/domain/entities/account"
	"vpn-shop/backend-go/internal/domain/entities/admin"
	"vpn-shop/backend-go/internal/domain/entities/payment"
	serverModel "vpn-shop/backend-go/internal/domain/entities/server"
	shared "vpn-shop/backend-go/internal/domain/entities/shared"
	"vpn-shop/backend-go/internal/domain/entities/user"
	"vpn-shop/backend-go/internal/application/usecases"
	"vpn-shop/backend-go/pkg"
)

// --- Structs for Tripay Payment Channels ---

// Fee represents the fee structure from Tripay.
type Fee struct {
	Flat    int64   `json:"flat"`
	Percent float64 `json:"percent"`
}

// TotalFee represents the total fee with a string for percent.
type TotalFee struct {
	Flat    int64  `json:"flat"`
	Percent string `json:"percent"`
}

// PaymentChannel defines the structure for a single payment channel.
type PaymentChannel struct {
	Group         string   `json:"group"`
	Code          string   `json:"code"`
	Name          string   `json:"name"`
	Type          string   `json:"type"`
	FeeMerchant   Fee      `json:"fee_merchant"`
	FeeCustomer   Fee      `json:"fee_customer"`
	TotalFee      TotalFee `json:"total_fee"`
	MinimumFee    *int64   `json:"minimum_fee"`
	MaximumFee    *int64   `json:"maximum_fee"`
	MinimumAmount int64    `json:"minimum_amount"`
	MaximumAmount int64    `json:"maximum_amount"`
	IconURL       string   `json:"icon_url"`
	Active        bool     `json:"active"`
}

// TripayChannelsResponse is the top-level API response from Tripay for payment channels.
type TripayChannelsResponse struct {
	Success bool             `json:"success"`
	Message string           `json:"message"`
	Data    []PaymentChannel `json:"data,omitempty"`
}

// PurchaseItem defines a single item in a purchase request.
type PurchaseItem struct {
	ServerID uint `json:"server_id"`
	Quantity int  `json:"quantity"`
}

// CreatePaymentRequest defines the structure for a product purchase request.
type CreatePaymentRequest struct {
	Items         []PurchaseItem `json:"items"`
	PaymentMethod string         `json:"payment_method"`
}

// --- Structs for Backend API ---

// TopUpRequest defines the payload for a top-up request.
type TopUpRequest struct {
	Amount        int64  `json:"amount"`
	PaymentMethod string `json:"payment_method"`
}

// TopUpHandler handles the creation of a new top-up transaction.
// @Summary Top-Up Balance
// @Description Initiates a new top-up transaction for the authenticated user.
// @Tags payments
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param topup_request body TopUpRequest true "Top-Up Request"
// @Success 200 {object} shared.TopUpResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /payment/topup [post]
func TopUpHandler(c echo.Context) error {
	// 1. Get required keys from environment
	apiKey := os.Getenv("TRIPAY_API_KEY")
	privateKey := os.Getenv("TRIPAY_PRIVATE_KEY")
	merchantCode := os.Getenv("TRIPAY_MERCHANT_CODE")
	callbackURL := os.Getenv("TRIPAY_CALLBACK_URL")

	if apiKey == "" || privateKey == "" || merchantCode == "" || callbackURL == "" {
		log.Error("One or more Tripay credentials are not set in .env")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan konfigurasi server."})
	}

	// 2. Parse and validate request from client
	var req TopUpRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}
	if req.Amount <= 0 {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Jumlah harus lebih besar dari nol"})
	}
	if req.PaymentMethod == "" {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Metode pembayaran diperlukan"})
	}

	// 3. Get user data from JWT
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		log.Error("Failed to get user ID from token: ", err)
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	var user user.User
	err = db.DB.Preload("Roles").First(&user, userID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
		}
		log.WithError(err).Errorf("Failed to find user with ID: %d", userID)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data pengguna"})
	}

	// 4. Validate top-up amount against admin settings
	var adminSettings admin.AdminSetting
	err = db.DB.First(&adminSettings).Error
	if err != nil {
		log.WithError(err).Error("Failed to get admin settings")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan konfigurasi server."})
	}

	isReseller := false
	for _, role := range user.Roles {
		if role.Name == "reseller" {
			isReseller = true
			break
		}
	}

	minimalTopUp := adminSettings.MinTopUp
	if isReseller {
		minimalTopUp = adminSettings.MinTopUpReseller
	}

	if req.Amount < minimalTopUp {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{
			Error: fmt.Sprintf("Minimum top-up amount is %d", minimalTopUp),
		})
	}

	// 5. Create transaction record in a database transaction
	invoiceID := "TOPUP-" + strconv.FormatUint(uint64(userID), 10) + "-" + time.Now().Format("20060102150405")
	transaction := payment.Transaction{
		InvoiceID:      invoiceID,
		UserID:         userID,
		Type:           payment.Topup,
		Description:    "Top Up Saldo",
		Amount:         req.Amount,
		Status:         payment.Pending,
		PaymentGateway: &[]string{"Tripay"}[0],
	}

	err = db.DB.Create(&transaction).Error
	if err != nil {
		log.WithError(err).Error("Failed to create transaction record")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memulai transaksi"})
	}

	// 6. Call Tripay Service to get checkout URL
	checkoutData, err := services.CreateTripayCheckoutURL(transaction, req.PaymentMethod, user, nil)
	if err != nil {
		log.WithError(err).Error("Failed to create Tripay checkout URL")
		db.DB.Model(&transaction).Update("status", payment.Failed)
		return c.JSON(http.StatusBadGateway, shared.ErrorResponse{
			Error:   "Failed to communicate with payment gateway",
			Details: err.Error(),
		})
	}

	// 7. Update transaction with gateway reference and URL
	if err := db.DB.Model(&transaction).Updates(map[string]interface{}{
		"gateway_reference":    checkoutData.Reference,
		"gateway_checkout_url": checkoutData.CheckoutURL,
	}).Error; err != nil {
		log.WithError(err).Error("Failed to update transaction with gateway info")
		// This is a critical error, the payment was created but we failed to save the reference.
		// Manual intervention might be needed.
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyimpan detail transaksi"})
	}

	// 8. Return checkout URL and reference to the client
	return c.JSON(http.StatusOK, map[string]interface{}{
		"checkout_url":      checkoutData.CheckoutURL,
		"gateway_reference": checkoutData.Reference,
		"invoice_id":        transaction.InvoiceID,
	})
}

// CreatePaymentHandler handles the creation of a new payment for product purchases.
// CreatePaymentHandler handles the creation of a new payment transaction for purchasing VPN products.
// @Summary Create Product Payment
// @Description Creates a new payment transaction for purchasing VPN products.
// @Tags payments
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param payment_request body CreatePaymentRequest true "Payment Request"
// @Success 200 {object} shared.TopUpResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /payment [post]
func CreatePaymentHandler(c echo.Context) error {
	// 1. Parse and validate request body
	var req CreatePaymentRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid", Details: err.Error()})
	}
	if len(req.Items) == 0 {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Item pembelian tidak boleh kosong"})
	}

	// 2. Extract user info from JWT
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	var currentUser user.User
	err = db.DB.Preload("Roles").First(&currentUser, userID).Error
	if err != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
	}

	isReseller := slices.ContainsFunc(currentUser.Roles, func(r *user.Role) bool { return r.Name == "reseller" })

	// 3. Fetch products and calculate total amount
	var totalAmount int64
	var orderItems []services.OrderItem
	var productDescription string

	tx := db.DB.Begin()
	if tx.Error != nil {
		log.WithError(tx.Error).Error("Failed to begin database transaction")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Terjadi kesalahan internal pada server"})
	}

	for i, item := range req.Items {
		var serverDetails serverModel.Server
		err = tx.First(&serverDetails, item.ServerID).Error
		if err != nil {
			tx.Rollback()
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: fmt.Sprintf("Produk dengan ID %d tidak ditemukan", item.ServerID)})
			}
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil detail produk"})
		}

		var price int64
		if isReseller {
			price = serverDetails.HargaReseller
		} else {
			price = serverDetails.HargaMember
		}

		itemAmount := price * int64(item.Quantity)
		totalAmount += itemAmount

		orderItems = append(orderItems, services.OrderItem{
			SKU:      serverDetails.Kode,
			Name:     serverDetails.Nama,
			Price:    int(price), // Tripay uses int for price
			Quantity: item.Quantity,
		})

		if i > 0 {
			productDescription += ", "
		}
		productDescription += fmt.Sprintf("%s (x%d)", serverDetails.Nama, item.Quantity)
	}

	// 4. Create a new transaction record
	invoiceID := "VPN-" + time.Now().Format("20060102150405") + "-" + strconv.Itoa(int(userID))
	tripayGateway := "TRIPAY"
	newTransaction := payment.Transaction{
		UserID:         userID,
		Amount:         totalAmount,
		Type:           payment.PurchaseMonthly, // Assuming monthly purchase for now
		Status:         payment.Pending,
		PaymentGateway: &tripayGateway,
		InvoiceID:      invoiceID,
		Description:    "Pembelian: " + productDescription,
	}

	err = tx.Create(&newTransaction).Error
	if err != nil {
		tx.Rollback()
		log.WithError(err).Error("Failed to create transaction record")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat transaksi"})
	}

	// Commit the transaction before calling the external API
	err = tx.Commit().Error
	if err != nil {
		log.WithError(err).Error("Failed to commit transaction before calling Tripay")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyelesaikan transaksi lokal"})
	}

	// 5. Call Tripay Service to get checkout URL
	checkoutData, err := services.CreateTripayCheckoutURL(newTransaction, req.PaymentMethod, currentUser, orderItems)
	if err != nil {
		log.WithError(err).Error("Failed to create Tripay checkout URL")
		db.DB.Model(&newTransaction).Update("status", payment.Failed)
		return c.JSON(http.StatusBadGateway, shared.ErrorResponse{
			Error:   "Failed to communicate with payment gateway",
			Details: err.Error(),
		})
	}

	// 6. Update transaction with gateway reference and URL
	if err := db.DB.Model(&newTransaction).Updates(map[string]interface{}{
		"gateway_reference":    checkoutData.Reference,
		"gateway_checkout_url": checkoutData.CheckoutURL,
	}).Error; err != nil {
		log.WithError(err).Warn("Failed to save gateway reference to transaction")
		// Not a fatal error, the payment can still proceed
	}

	// 7. Return checkout URL and reference to the client
	return c.JSON(http.StatusOK, map[string]interface{}{
		"checkout_url":      checkoutData.CheckoutURL,
		"gateway_reference": checkoutData.Reference,
		"invoice_id":        newTransaction.InvoiceID,
	})
}

// extendAccountExpiry finds the correct user account, extends its expiry date, and returns the updated account and new expiry time.
func extendAccountExpiry(tx *gorm.DB, accountID uint, accountType string, monthsToAdd int) (account.IAccount, time.Time, error) {
	var currentAccount account.IAccount
	var tableName string

	switch accountType {
	case "trojan":
		var acc account.AccountTrojan
		if err := tx.First(&acc, accountID).Error; err != nil {
			return nil, time.Time{}, fmt.Errorf("account_trojan with ID %d not found: %w", accountID, err)
		}
		currentAccount = &acc
		tableName = acc.TableName()
	case "vmess":
		var acc account.AccountVmess
		if err := tx.First(&acc, accountID).Error; err != nil {
			return nil, time.Time{}, fmt.Errorf("account_vmess with ID %d not found: %w", accountID, err)
		}
		currentAccount = &acc
		tableName = acc.TableName()
	case "vless":
		var acc account.AccountVless
		if err := tx.First(&acc, accountID).Error; err != nil {
			return nil, time.Time{}, fmt.Errorf("account_vless with ID %d not found: %w", accountID, err)
		}
		currentAccount = &acc
		tableName = acc.TableName()
	case "ssh":
		var acc account.AccountSsh
		if err := tx.First(&acc, accountID).Error; err != nil {
			return nil, time.Time{}, fmt.Errorf("account_ssh with ID %d not found: %w", accountID, err)
		}
		currentAccount = &acc
		tableName = acc.TableName()
	default:
		// Fallback for old types, assuming they map to ssh
		// This should be updated or removed once the system is consistent
		log.Warnf("Unknown or legacy account type '%s', falling back to 'ssh'", accountType)
		var acc account.AccountSsh
		if err := tx.First(&acc, accountID).Error; err != nil {
			return nil, time.Time{}, fmt.Errorf("fallback account_ssh with ID %d not found: %w", accountID, err)
		}
		currentAccount = &acc
		tableName = acc.TableName()
	}

	expiry := time.Now()
	if currentAccount.GetExpiredDate() != nil {
		expiry = *currentAccount.GetExpiredDate()
	}

	newExpiry := expiry
	if newExpiry.Before(time.Now()) {
		newExpiry = time.Now()
	}
	newExpiry = newExpiry.AddDate(0, monthsToAdd, 0)

	if err := tx.Table(tableName).Where("id = ?", accountID).Update("expired", newExpiry).Error; err != nil {
		return nil, time.Time{}, fmt.Errorf("failed to update expiry date for account %d: %w", accountID, err)
	}

	log.Infof("Successfully extended account type '%s' with ID %d by %d months. New expiry: %s", accountType, accountID, monthsToAdd, newExpiry.Format("2006-01-02"))
	return currentAccount, newExpiry, nil
}

// handleFailedPaymentAsRefund processes a refund to the user's balance when a post-payment action fails.
func handleFailedPaymentAsRefund(c echo.Context, originalTransaction *payment.Transaction, failureReason string) error {
	refundTx := db.DB.Begin()
	if refundTx.Error != nil {
		log.WithError(refundTx.Error).Error("Failed to begin refund transaction")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses refund: tidak bisa memulai transaksi"})
	}

	// Get user
	var user user.User
	if err := refundTx.First(&user, originalTransaction.UserID).Error; err != nil {
		refundTx.Rollback()
		log.WithError(err).Errorf("Failed to find user %d for refund", originalTransaction.UserID)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses refund: pengguna tidak ditemukan"})
	}

	// Add balance to user
	newBalance := user.Saldo + originalTransaction.Amount
	if err := refundTx.Model(&user).Update("saldo", newBalance).Error; err != nil {
		refundTx.Rollback()
		log.WithError(err).Errorf("Failed to refund balance to user %d", user.ID)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses refund: tidak bisa mengembalikan saldo"})
	}

	// Create a new refund transaction record
	refundRecord := payment.Transaction{
		InvoiceID:   fmt.Sprintf("REF-%s", originalTransaction.InvoiceID),
		UserID:      user.ID,
		Type:        payment.Refund,
		Description: fmt.Sprintf("Refund otomatis untuk transaksi gagal (invoice: %s). Alasan: %s", originalTransaction.InvoiceID, failureReason),
		Amount:      originalTransaction.Amount,
		Status:      payment.Success,
	}
	if err := refundTx.Create(&refundRecord).Error; err != nil {
		refundTx.Rollback()
		log.WithError(err).Error("Failed to create refund transaction record")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses refund: tidak bisa membuat catatan refund"})
	}

	// Mark original transaction as FAILED
	if err := refundTx.Model(&payment.Transaction{}).Where("id = ?", originalTransaction.ID).Update("status", payment.Failed).Error; err != nil {
		refundTx.Rollback()
		log.WithError(err).Error("Failed to mark original transaction as FAILED")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses refund: tidak bisa update transaksi asli"})
	}

	if err := refundTx.Commit().Error; err != nil {
		log.WithError(err).Error("Failed to commit refund transaction")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses refund: gagal finalisasi"})
	}

	log.Infof("Successfully refunded %d to user %d for failed %s transaction %s", originalTransaction.Amount, originalTransaction.UserID, originalTransaction.Type, originalTransaction.InvoiceID)
	// Return a generic error message to the user, including the specific reason for the failure.
	finalErrorMessage := fmt.Sprintf("Terjadi kegagalan: %s. Dana Anda telah dikembalikan ke saldo.", failureReason)
	return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: finalErrorMessage})
}

func processNewPurchase(tx *gorm.DB, transaction *payment.Transaction) error {
	var username, kodeServer, protocol string
	var bulan int

	// 1. Try to get purchase details from OrderItem first (for Tripay transactions)
	var orderItem services.OrderItem
	err := tx.Where("merchant_ref = ?", transaction.InvoiceID).First(&orderItem).Error

	if err == nil {
		// Found OrderItem, parse from SKU
		// SKU format: PURCHASE|kode_server|protocol|username|bulan
		parts := strings.Split(orderItem.SKU, "|")
		if len(parts) == 5 && parts[0] == "PURCHASE" {
			kodeServer = parts[1]
			protocol = parts[2]
			username = parts[3]
			bulan, _ = strconv.Atoi(parts[4])
		} else {
			return fmt.Errorf("format SKU tidak valid untuk pembelian: '%s'", orderItem.SKU)
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 2. Fallback to parsing description (for old SALDO transactions)
		// Example: "Pembelian Akun perbulan123(gcloud-1) - 1 bulan"
		re := regexp.MustCompile(`Pembelian Akun (.+?)\((.+?)\) - (\d+) bulan`)
		matches := re.FindStringSubmatch(transaction.Description)
		if len(matches) < 4 { // Adjusted to 4 as protocol is missing
			return fmt.Errorf("deskripsi transaksi tidak valid: '%s'", transaction.Description)
		}
		username = matches[1]
		kodeServer = matches[2]
		bulan, _ = strconv.Atoi(matches[3])
		protocol = "vmess" // Defaulting for old transactions
	} else {
		return fmt.Errorf("gagal mengambil data order item: %w", err)
	}

	if username == "" || kodeServer == "" || protocol == "" || bulan == 0 {
		return fmt.Errorf("gagal mem-parsing detail pembelian dari transaksi")
	}

	// Step 2: Get server details from DB.
	var server serverModel.Server
	err = tx.Where("kode = ?", kodeServer).First(&server).Error
	if err != nil {
		return fmt.Errorf("server '%s' tidak ditemukan", kodeServer)
	}

	decryptedToken, err := utils.Decrypt(server.Token)
	if err != nil {
		return fmt.Errorf("gagal dekripsi token untuk server %s", kodeServer)
	}

	// Step 3: Generate new password/uuid.
	newPassword := uuid.New().String()

	// Step 4: Create user on Marzban server.
	expiryDate := time.Now().AddDate(0, bulan, 0)
	var proxies map[string]interface{}
	switch protocol {
	case "trojan":
		proxies = map[string]interface{}{
			"trojan": services.CreateTrojanProxySettings(newPassword), // Using UUID as password for simplicity
		}
	case "vmess":
		proxies = map[string]interface{}{
			"vmess": services.CreateVmessProxySettings(newPassword),
		}
	case "vless":
		proxies = map[string]interface{}{
			"vless": services.CreateVlessProxySettings(newPassword),
		}
	default:
		return fmt.Errorf("protokol tidak didukung: %s", protocol)
	}

	payload := services.MarzbanUserRequest{
		Username: username,
		Proxies:  proxies,
		Inbounds: map[string][]string{
			protocol: {},
		},
		Expire:                 expiryDate.Unix(),
		DataLimit:              1000 * 1024 * 1024 * 1024, // 1000 GB
		DataLimitResetStrategy: "no_reset",
		Status:                 "active",
		Note:                   fmt.Sprintf("Pembelian %d bulan", bulan),
	}

	if err := services.CreateUser(server.Domain, decryptedToken, payload); err != nil {
		return fmt.Errorf("gagal membuat pengguna di Marzban: %w", err)
	}

	// Step 5: Create account record in local DB.
	var newAccount account.IAccount

	switch protocol {
	case "trojan":
		kodeAkun, err := utils.GenerateRandomString(10)
		if err != nil {
			return fmt.Errorf("gagal membuat kode akun: %w", err)
		}
		acc := &account.AccountTrojan{
			UserID:           fmt.Sprintf("%d", transaction.UserID),
			KodeServer:       kodeServer,
			KodeAkun:         kodeAkun,
			Domain:           server.Domain,
			Durasi:           fmt.Sprintf("%d", bulan),
			Username:         username,
			UUID:             newPassword,
			Expired:          &expiryDate,
			TanggalBeli:      time.Now(),
			Status:           "active",
			SubscriptionType: "montly",
			Harga:            fmt.Sprintf("%d", transaction.Amount),
		}
		if err := tx.Create(acc).Error; err != nil {
			return fmt.Errorf("gagal menyimpan akun trojan baru: %w", err)
		}
		newAccount = acc
	case "vmess", "vless":
		tableName := "account_vmess"
		if protocol == "vless" {
			tableName = "account_vless"
		}
		kodeAkun, err := utils.GenerateRandomString(10)
		if err != nil {
			return fmt.Errorf("gagal membuat kode akun: %w", err)
		}
		acc := &account.AccountVmess{
			UserID:           fmt.Sprintf("%d", transaction.UserID),
			KodeServer:       kodeServer,
			KodeAkun:         kodeAkun,
			Domain:           server.Domain,
			Durasi:           fmt.Sprintf("%d", bulan),
			Username:         username,
			UUID:             newPassword,
			Expired:          &expiryDate,
			TanggalBeli:      time.Now(),
			Status:           "active",
			SubscriptionType: "montly",
			Harga:            fmt.Sprintf("%d", transaction.Amount),
		}
		if err := tx.Table(tableName).Create(acc).Error; err != nil {
			return fmt.Errorf("gagal menyimpan akun %s baru: %w", protocol, err)
		}
		if protocol == "vless" {
			newAccount = (*account.AccountVless)(acc)
		} else {
			newAccount = acc
		}
	default:
		return fmt.Errorf("protokol tidak didukung: %s", protocol)
	}

	// Step 6: Update the original transaction with the new account's ID.
	if err := tx.Model(transaction).Updates(map[string]interface{}{
		"account_id":   newAccount.GetID(),
		"account_type": &protocol,
	}).Error; err != nil {
		log.Errorf("CRITICAL: Failed to link transaction %s to new account %d: %v", transaction.InvoiceID, newAccount.GetID(), err)
		return fmt.Errorf("gagal memperbarui transaksi dengan ID akun baru: %w", err)
	}

	// Step 7: Update server statistics (slot_terpakai and total_user)
	if err := tx.Model(&serverModel.Server{}).Where("kode = ?", kodeServer).Updates(map[string]interface{}{
		"slot_terpakai": gorm.Expr("slot_terpakai + ?", 1),
		"total_user":    gorm.Expr("total_user + ?", 1),
	}).Error; err != nil {
		return fmt.Errorf("gagal memperbarui statistik server: %w", err)
	}

	// Step 8: Update user payment statistics
	monthlyAmount := transaction.Amount
	if err := tx.Model(&user.User{}).Where("id = ?", transaction.UserID).Updates(map[string]interface{}{
		"pay_bulanan": gorm.Expr("pay_bulanan + ?", monthlyAmount),
		"total_pay":   gorm.Expr("total_pay + ?", monthlyAmount),
	}).Error; err != nil {
		return fmt.Errorf("gagal memperbarui statistik pembayaran user: %w", err)
	}

	// Create notification for successful Tripay purchase
	if err := services.CreateMonthlyPurchaseNotification(transaction.UserID, newAccount.GetUsername(), kodeServer, bulan, transaction.Amount); err != nil {
		log.WithError(err).Warnf("Failed to create monthly purchase notification for user %d", transaction.UserID)
		// Don't fail the transaction if notification fails
	}

	log.Infof("Successfully created and provisioned new '%s' account (ID: %d) for user %d", protocol, newAccount.GetID(), transaction.UserID)
	return nil
}

// TripayCallbackHandler handles notifications from Tripay.
// @ignore
// @Summary Tripay Payment Callback
// @Description Handles incoming webhook notifications from Tripay to update transaction status.
// @Tags payments
// @Accept json
// @Produce json
// @Success 200 {object} shared.SuccessResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
func TripayCallbackHandler(c echo.Context) error {
	privateKey := os.Getenv("TRIPAY_PRIVATE_KEY")
	if privateKey == "" {
		log.Error("Tripay private key not found in .env")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan konfigurasi server"})
	}

	// Read request body
	bodyBytes, err := io.ReadAll(c.Request().Body)
	if err != nil {
		log.Errorf("Failed to read callback body: %v", err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses callback"})
	}

	// Log the raw callback body for debugging
	log.Infof("Menerima payload callback dari Tripay: %s", string(bodyBytes))

	// Validate signature
	signature := c.Request().Header.Get("X-Callback-Signature")
	mac := hmac.New(sha256.New, []byte(privateKey))
	mac.Write(bodyBytes)
	expectedSignature := hex.EncodeToString(mac.Sum(nil))

	if signature != expectedSignature {
		log.Warnf("Invalid callback signature. Received: %s, Expected: %s", signature, expectedSignature)
		return c.JSON(http.StatusForbidden, shared.ErrorResponse{Error: "Tanda tangan tidak valid"})
	}

	var payload services.TripayCallbackPayload
	if err := json.Unmarshal(bodyBytes, &payload); err != nil {
		log.Errorf("Failed to parse JSON payload from callback: %v", err)
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format payload tidak valid"})
	}

	log.Infof("Tripay callback received for Merchant Ref: %s, Status: %s", payload.MerchantRef, payload.Status)

	// Find the transaction in our database
	var transaction payment.Transaction
	if err := db.DB.Where("invoice_id = ?", payload.MerchantRef).First(&transaction).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Errorf("Transaction with Invoice ID %s not found.", payload.MerchantRef)
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Transaksi tidak ditemukan"})
		}
		log.WithError(err).Error("Failed to query transaction")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses callback"})
	}

	// If transaction is already processed, ignore the callback to prevent double processing
	if transaction.Status != payment.Pending {
		log.Warnf("Transaction %s already in status %s. Ignoring callback.", transaction.InvoiceID, transaction.Status)
		return c.JSON(http.StatusOK, shared.SuccessResponse{Success: true, Message: "Transaksi sudah diproses"})
	}

	tx := db.DB.Begin()
	if tx.Error != nil {
		log.WithError(tx.Error).Error("Failed to begin database transaction")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses callback"})
	}

	switch payload.Status {
	case "PAID":
		transaction.Status = payment.Success
		if err := tx.Save(&transaction).Error; err != nil {
			tx.Rollback()
			log.WithError(err).Error("Failed to update transaction status to SUCCESS")
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui transaksi"})
		}

		switch transaction.Type {
		case payment.Topup:
			if err := tx.Model(&user.User{}).Where("id = ?", transaction.UserID).Update("saldo", gorm.Expr("saldo + ?", transaction.Amount)).Error; err != nil {
				tx.Rollback()
				log.WithError(err).Errorf("Failed to add balance for User ID %d", transaction.UserID)
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui saldo pengguna"})
			}
			log.Infof("Balance successfully added for User ID %d from transaction %s", transaction.UserID, transaction.InvoiceID)

			// Create notification for successful top-up
			if err := services.CreateTopupNotification(transaction.UserID, transaction.Amount, "Tripay"); err != nil {
				log.WithError(err).Warnf("Failed to create topup notification for user %d", transaction.UserID)
				// Don't fail the transaction if notification fails
			}
		case payment.PurchaseMonthly:
			if err := processNewPurchase(tx, &transaction); err != nil {
				tx.Rollback()
				log.WithError(err).Errorf("Gagal memproses pembelian baru untuk transaksi %s. Refund diproses.", transaction.InvoiceID)
				return handleFailedPaymentAsRefund(c, &transaction, err.Error())
			}
			log.Infof("Successfully processed monthly purchase for transaction %s", transaction.InvoiceID)

		case payment.Renewal:
			log.Infof("Processing renewal for transaction %s", transaction.InvoiceID)
			if transaction.AccountID == nil || transaction.AccountType == nil {
				tx.Rollback()
				log.Errorf("Renewal transaction %s is missing AccountID or AccountType", transaction.InvoiceID)
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Data transaksi perpanjangan tidak valid"})
			}

			// Extract duration from description, e.g., "Perpanjangan Akun ... - 3 bulan"
			re := regexp.MustCompile(`(\d+)\s+bulan`)
			matches := re.FindStringSubmatch(transaction.Description)
			if len(matches) < 2 {
				tx.Rollback()
				log.Errorf("Could not parse renewal duration from description: '%s'", transaction.Description)
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Deskripsi transaksi untuk perpanjangan tidak valid"})
			}

			months, err := strconv.Atoi(matches[1])
			if err != nil {
				tx.Rollback()
				log.Errorf("Could not convert renewal duration '%s' to int: %v", matches[1], err)
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Format durasi perpanjangan tidak valid"})
			}

			updatedAccount, newExpiry, err := extendAccountExpiry(tx, *transaction.AccountID, *transaction.AccountType, months)
			if err != nil {
				tx.Rollback()
				log.WithError(err).Errorf("Failed to process account renewal for transaction %s", transaction.InvoiceID)
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperpanjang masa aktif akun", Details: err.Error()})
			}

			// Sync with Marzban server
			if err := services.RenewMarzbanUser(c.Logger(), tx, updatedAccount.GetKodeServer(), updatedAccount.GetUsername(), newExpiry); err != nil {
				// Rollback the current transaction to undo expiry extension
				tx.Rollback()
				log.WithError(err).Errorf("Failed to sync renewal with Marzban for transaction %s. Initiating refund to user balance.", transaction.InvoiceID)

				// Handle the failure by refunding the user and updating statuses
				return handleFailedPaymentAsRefund(c, &transaction, "Gagal sinkronisasi dengan server VPN")
			}

			// Update user payment statistics for renewal
			if err := tx.Model(&user.User{}).Where("id = ?", transaction.UserID).Updates(map[string]interface{}{
				"pay_bulanan": gorm.Expr("pay_bulanan + ?", transaction.Amount),
				"total_pay":   gorm.Expr("total_pay + ?", transaction.Amount),
			}).Error; err != nil {
				log.WithError(err).Warnf("Failed to update user payment stats for renewal transaction %s", transaction.InvoiceID)
				// Don't rollback for this, as the renewal was successful
			}

			log.Infof("Successfully processed and synced renewal for transaction %s", transaction.InvoiceID)
		}

	case "EXPIRED":
		transaction.Status = payment.Expired
		if err := tx.Save(&transaction).Error; err != nil {
			tx.Rollback()
			log.WithError(err).Error("Failed to update transaction status to EXPIRED")
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui transaksi"})
		}
		log.Infof("Transaction %s marked as %s.", transaction.InvoiceID, transaction.Status)

	case "FAILED":
		transaction.Status = payment.Failed
		if err := tx.Save(&transaction).Error; err != nil {
			tx.Rollback()
			log.WithError(err).Error("Failed to update transaction status to FAILED")
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui transaksi"})
		}
		log.Infof("Transaction %s marked as %s.", transaction.InvoiceID, transaction.Status)

	default:
		log.Warnf("Received unknown callback status: %s for transaction %s", payload.Status, transaction.InvoiceID)
	}

	if err := tx.Commit().Error; err != nil {
		log.WithError(err).Error("Failed to commit database transaction")
		// Note: The transaction was processed but commit failed. This may require manual checking.
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyelesaikan proses callback"})
	}

	return c.JSON(http.StatusOK, shared.SuccessResponse{Success: true})
}

// GetPaymentChannelsHandler fetches the available payment channels from Tripay.
// @Summary Get Payment Channels
// @Description Retrieves a list of available payment channels from the payment gateway.
// @Tags payments
// @Produce json
// @Security BearerAuth
// @Success 200 {object} TripayChannelsResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /payment/channels [get]
func GetPaymentChannelsHandler(c echo.Context) error {
	// 1. Get Tripay API Key from environment variables
	apiKey := os.Getenv("TRIPAY_API_KEY")
	if apiKey == "" {
		log.Error("TRIPAY_API_KEY is not set in .env")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan konfigurasi server."})
	}

	// 2. Prepare the request to Tripay
	baseURL := os.Getenv("TRIPAY_BASE_URL")
	if baseURL == "" {
		log.Error("TRIPAY_BASE_URL is not set in .env")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan konfigurasi server."})
	}
	url := fmt.Sprintf("%s/merchant/payment-channel", baseURL)
	transport := &http.Transport{
		DisableKeepAlives: true, // Nonaktifkan keep-alives untuk mencegah error EOF
	}
	client := &http.Client{
		Timeout:   15 * time.Second,
		Transport: transport,
	}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.WithFields(log.Fields{"error": err.Error()}).Error("Failed to create request to Tripay")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Failed to create payment request"})
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	// Menambahkan User-Agent untuk menghindari masalah koneksi seperti EOF
	req.Header.Set("User-Agent", "vpn-shop-go-client/1.0")

	resp, err := client.Do(req)
	if err != nil {
		log.WithFields(log.Fields{"error": err.Error()}).Error("Failed to get payment channels from Tripay")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Failed to communicate with payment gateway"})
	}
	defer resp.Body.Close()

	// 4. Read and parse the response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("Failed to read response body from Tripay")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membaca respons dari gateway pembayaran."})
	}

	if resp.StatusCode != http.StatusOK {
		log.Errorf("Tripay returned non-OK status: %d. Body: %s", resp.StatusCode, string(bodyBytes))
		return c.JSON(http.StatusBadGateway, shared.ErrorResponse{
			Error:   fmt.Sprintf("Gateway pembayaran mengembalikan error (Status: %d)", resp.StatusCode),
			Details: string(bodyBytes),
		})
	}

	var tripayResponse TripayChannelsResponse
	if err := json.Unmarshal(bodyBytes, &tripayResponse); err != nil {
		log.WithError(err).Error("Failed to unmarshal Tripay response")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Format respons dari gateway pembayaran tidak valid."})
	}

	if !tripayResponse.Success {
		log.Warnf("Tripay API returned success=false. Message: %s", tripayResponse.Message)
		return c.JSON(http.StatusBadGateway, shared.ErrorResponse{
			Error:   "Gagal mengambil daftar channel pembayaran.",
			Details: tripayResponse.Message,
		})
	}

	// 5. Return the data to the client
	return c.JSON(http.StatusOK, tripayResponse)
}

// TripayTransactionStatus represents the transaction status response from Tripay
type TripayTransactionStatus struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Reference         string `json:"reference"`
		MerchantRef       string `json:"merchant_ref"`
		PaymentMethod     string `json:"payment_method"`
		PaymentMethodName string `json:"payment_method_name"`
		CustomerName      string `json:"customer_name"`
		CustomerEmail     string `json:"customer_email"`
		CustomerPhone     string `json:"customer_phone"`
		CallbackURL       string `json:"callback_url"`
		ReturnURL         string `json:"return_url"`
		Amount            int64  `json:"amount"`
		Fee               int64  `json:"fee"`
		TotalFee          int64  `json:"total_fee"`
		AmountReceived    int64  `json:"amount_received"`
		Status            string `json:"status"`
		PaidAt            *int64 `json:"paid_at"`
		Note              string `json:"note"`
	} `json:"data"`
}

// CheckPaymentStatusHandler checks the payment status from Tripay API
// @Summary Check Payment Status
// @Description Checks the payment status of a transaction using Tripay API
// @Tags payments
// @Produce json
// @Security BearerAuth
// @Param reference path string true "Transaction Reference/Invoice ID"
// @Success 200 {object} TripayTransactionStatus
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /payment/status/{reference} [get]
func CheckPaymentStatusHandler(c echo.Context) error {
	// 1. Get reference from URL parameter
	reference := c.Param("reference")
	if reference == "" {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Reference transaksi diperlukan"})
	}

	// 2. Get Tripay credentials from environment
	apiKey := os.Getenv("TRIPAY_API_KEY")
	privateKey := os.Getenv("TRIPAY_PRIVATE_KEY")
	merchantCode := os.Getenv("TRIPAY_MERCHANT_CODE")
	baseURL := os.Getenv("TRIPAY_BASE_URL")

	if apiKey == "" || privateKey == "" || merchantCode == "" || baseURL == "" {
		log.Error("One or more Tripay credentials are not set in .env")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan konfigurasi server"})
	}

	// 3. Create signature for Tripay API
	// Signature = HMAC-SHA256(merchant_code + reference + private_key)
	signaturePayload := merchantCode + reference + privateKey
	mac := hmac.New(sha256.New, []byte(privateKey))
	mac.Write([]byte(signaturePayload))
	signature := hex.EncodeToString(mac.Sum(nil))

	// 4. Prepare HTTP request to Tripay
	url := fmt.Sprintf("%s/transaction/detail?reference=%s", baseURL, reference)
	transport := &http.Transport{
		DisableKeepAlives: true,
	}
	client := &http.Client{
		Timeout:   15 * time.Second,
		Transport: transport,
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.WithError(err).Error("Failed to create request to Tripay")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat permintaan ke gateway pembayaran"})
	}

	// 5. Set headers
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("X-Signature", signature)
	req.Header.Set("User-Agent", "vpn-shop-go-client/1.0")

	// 6. Execute request
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("Failed to get transaction status from Tripay")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal berkomunikasi dengan gateway pembayaran"})
	}
	defer resp.Body.Close()

	// 7. Read response
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("Failed to read response body from Tripay")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membaca respons dari gateway pembayaran"})
	}

	log.Infof("Tripay status check response for %s: %s", reference, string(bodyBytes))

	// 8. Handle different HTTP status codes
	if resp.StatusCode == http.StatusNotFound {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Transaksi tidak ditemukan"})
	}

	if resp.StatusCode != http.StatusOK {
		log.Errorf("Tripay returned non-OK status: %d. Body: %s", resp.StatusCode, string(bodyBytes))
		return c.JSON(http.StatusBadGateway, shared.ErrorResponse{
			Error:   fmt.Sprintf("Gateway pembayaran mengembalikan error (Status: %d)", resp.StatusCode),
			Details: string(bodyBytes),
		})
	}

	// 9. Parse response
	var tripayResponse TripayTransactionStatus
	if err := json.Unmarshal(bodyBytes, &tripayResponse); err != nil {
		log.WithError(err).Error("Failed to unmarshal Tripay status response")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Format respons dari gateway pembayaran tidak valid"})
	}

	if !tripayResponse.Success {
		log.Warnf("Tripay API returned success=false for reference %s. Message: %s", reference, tripayResponse.Message)
		return c.JSON(http.StatusBadGateway, shared.ErrorResponse{
			Error:   "Gagal mengecek status pembayaran",
			Details: tripayResponse.Message,
		})
	}

	// 10. Return the status to client
	return c.JSON(http.StatusOK, tripayResponse)
}
