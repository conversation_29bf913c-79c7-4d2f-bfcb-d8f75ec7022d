package account

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"

	"vpn-shop/backend-go/internal/domain/entities/shared"
	"vpn-shop/backend-go/internal/application/usecases"
	"vpn-shop/backend-go/pkg"

	"github.com/labstack/echo/v4"
)

// GetAccountDetail handles the request to get detailed account information.
// @Summary Get account detail
// @Description Retrieves detailed information for a specific VPN account, combining local data and live data from the Marzban API.
// @Tags Accounts
// @Accept json
// @Produce json
// @Param username path string true "Account Username"
// @Param server_code path string true "Server Code"
// @Success 200 {object} shared.AccountDetailResponse
// @Failure 404 {object} shared.ErrorResponse "Server or account not found"
// @Failure 500 {object} shared.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /accounts/detail/{username}/{server_code} [get]
func GetAccountDetail(c echo.Context) error {
	username := c.Param("username")
	serverCode := c.Param("server_code")

	localAccount, err := services.FindAccountByUsernameAndServer(username, serverCode)
	if err != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Account not found on the specified server"})
	}

	decryptedToken, err := utils.Decrypt(localAccount.Server.Token)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Failed to process server credentials"})
	}

	marzbanUser, err := services.GetUserDetails(localAccount.Server.Domain, decryptedToken, username)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: fmt.Sprintf("Failed to fetch data from Marzban: %v", err)})
	}
	processedLinks := processMarzbanLinks(marzbanUser.Links)

	subscriptionURL := fmt.Sprintf("%s%s", localAccount.Server.Domain, marzbanUser.SubscriptionURL)

	var expiredDate time.Time
	if localAccount.ExpiredDate != nil {
		expiredDate = *localAccount.ExpiredDate
	}

	response := shared.AccountDetailResponse{
		Username:        localAccount.Username,
		UUID:            localAccount.Password,
		AccountType:     localAccount.AccountType,
		Status:          localAccount.Status,
		Protocol:        localAccount.AccountType,
		ExpiredDate:     expiredDate,
		DataLimitGB:     bytesToGB(marzbanUser.DataLimit),
		UsedTrafficGB:   bytesToGB(marzbanUser.UsedTraffic),
		SubscriptionURL: subscriptionURL,
		ConnectionLinks: processedLinks,
		Server:          localAccount.Server.ToServerInfoForAccountDetail(),
	}

	return c.JSON(http.StatusOK, response)
}

// extractConnectionName parses the connection URL to find the real display name.
// For vmess, it decodes the base64 JSON and gets the 'ps' field.
// For vless and trojan, it decodes the URL fragment.
func extractConnectionName(link string) string {
	parsedURL, err := url.Parse(link)
	if err != nil {
		return "Invalid Link"
	}

	protocol := strings.ToLower(parsedURL.Scheme)

	switch protocol {
	case "trojan", "vless":
		if parsedURL.Fragment != "" {
			decodedName, err := url.QueryUnescape(parsedURL.Fragment)
			if err == nil {
				return decodedName
			}
		}
	case "vmess":
		base64Config := strings.TrimPrefix(link, "vmess://")
		jsonConfig, err := base64.StdEncoding.DecodeString(base64Config)
		if err != nil {
			return "VMess (Base64 Decode Error)"
		}

		var vmessConfig struct {
			Ps string `json:"ps"`
		}

		if err := json.Unmarshal(jsonConfig, &vmessConfig); err == nil {
			if vmessConfig.Ps != "" {
				return vmessConfig.Ps
			}
		}
	}
	return strings.ToUpper(protocol)
}

func processMarzbanLinks(rawLinks []string) []shared.ProcessedLink {
	var processedLinks []shared.ProcessedLink
	for _, link := range rawLinks {
		if link == "" {
			continue
		}
		name := extractConnectionName(link)
		processedLinks = append(processedLinks, shared.ProcessedLink{
			Name: name,
			URL:  link,
		})
	}
	return processedLinks
}

func bytesToGB(bytes int64) float64 {
	if bytes <= 0 {
		return 0
	}
	gb := float64(bytes) / (1024 * 1024 * 1024)
	return math.Round(gb*100) / 100
}
