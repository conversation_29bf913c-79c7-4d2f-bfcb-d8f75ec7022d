package user

import (
	"net/http"
	"sort"
	"strconv"
	"strings"
	"vpn-shop/backend-go/internal/infrastructure/database"
	"vpn-shop/backend-go/internal/infrastructure/http/middleware"
	account "vpn-shop/backend-go/internal/domain/entities/account"
	server "vpn-shop/backend-go/internal/domain/entities/server"
	"vpn-shop/backend-go/internal/domain/entities/shared"
	user "vpn-shop/backend-go/internal/domain/entities/user"

	"github.com/labstack/echo/v4"
)

// GetHistory retrieves the service history for the authenticated user.
// @Summary Get user's service history
// @Description Get a list of all purchased services (Trojan, Vmess, Vless, SSH) for the logged-in user.
// @Tags Accounts
// @Security BearerAuth
// @Produce json
// @Success 200 {object} user.ServiceHistoryResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /accounts/history [get]
func GetHistory(c echo.Context) error {
	// Retrieve user from context
	userIDStr, err := middleware.GetUserIDStringFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	// --- Fetch server data for ISP mapping ---
	var servers []server.Server
	db.DB.Find(&servers)
	serverMap := make(map[string]string) // domain -> ISP name
	for _, srv := range servers {
		serverMap[srv.Domain] = srv.NamaIsp
	}

	// --- Fetch all history items first ---
	var allHistory []user.ServiceHistoryItem
	// Fetch Trojan, Vmess, Vless, SSH accounts and append to allHistory...
	// Fetch Trojan accounts
	var trojans []account.AccountTrojan
	db.DB.Where("user_id = ?", userIDStr).Find(&trojans)
	for _, t := range trojans {
		allHistory = append(allHistory, user.ServiceHistoryItem{
			TanggalBeli: t.TanggalBeli,
			Username:    t.Username,
			Layanan:     t.Domain,
			NamaIsp:     serverMap[t.Domain],
			Status:      t.Status,
			Tipe:        "trojan-" + t.SubscriptionType,
			ServiceType: "trojan",
			OrderID:     t.KodeAkun, // Menggunakan KodeAkun sebagai OrderID
			KodeServer:  t.KodeServer,
			KodeAkun:    t.KodeAkun,
			Expired:     t.Expired,
		})
	}

	// Fetch Vmess accounts
	var vmess []account.AccountVmess
	db.DB.Where("user_id = ?", userIDStr).Find(&vmess)
	for _, v := range vmess {
		allHistory = append(allHistory, user.ServiceHistoryItem{
			TanggalBeli: v.TanggalBeli,
			Username:    v.Username,
			Layanan:     v.Domain,
			NamaIsp:     serverMap[v.Domain],
			Status:      v.Status,
			Tipe:        "vmess-" + v.SubscriptionType,
			ServiceType: "vmess",
			OrderID:     v.KodeAkun, // Menggunakan KodeAkun sebagai OrderID
			KodeServer:  v.KodeServer,
			KodeAkun:    v.KodeAkun,
			Expired:     v.Expired,
		})
	}

	// Fetch Vless accounts
	var vless []account.AccountVless
	db.DB.Where("user_id = ?", userIDStr).Find(&vless)
	for _, v := range vless {
		allHistory = append(allHistory, user.ServiceHistoryItem{
			TanggalBeli: v.TanggalBeli,
			Username:    v.Username,
			Layanan:     v.Domain,
			NamaIsp:     serverMap[v.Domain],
			Status:      v.Status,
			Tipe:        "vless-" + v.SubscriptionType,
			ServiceType: "vless",
			OrderID:     v.KodeAkun, // Menggunakan KodeAkun sebagai OrderID
			KodeServer:  v.KodeServer,
			KodeAkun:    v.KodeAkun,
			Expired:     v.Expired,
		})
	}

	// Fetch SSH accounts
	var ssh []account.AccountSsh
	db.DB.Where("user_id = ?", userIDStr).Find(&ssh)
	for _, s := range ssh {
		allHistory = append(allHistory, user.ServiceHistoryItem{
			TanggalBeli: s.TanggalBeli,
			Username:    s.Username,
			Layanan:     s.Domain,
			NamaIsp:     serverMap[s.Domain],
			Status:      s.Status,
			Tipe:        "ssh-" + s.SubscriptionType,
			ServiceType: "ssh",
			OrderID:     s.KodeAkun, // Menggunakan KodeAkun sebagai OrderID
			KodeServer:  s.KodeServer,
			KodeAkun:    s.KodeAkun,
			Expired:     s.Expired,
		})
	}

	// Karena KodeServer telah dihapus dari model ServiceHistoryItem,
	// kita tidak perlu lagi mengayakan data dengan nama server

	// --- Search and Filter ---
	searchQuery := strings.ToLower(c.QueryParam("search"))
	var filteredHistory []user.ServiceHistoryItem
	if searchQuery != "" {
		for _, item := range allHistory {
			if strings.Contains(strings.ToLower(item.Username), searchQuery) ||
				strings.Contains(strings.ToLower(item.Layanan), searchQuery) ||
				strings.Contains(strings.ToLower(item.Tipe), searchQuery) {
				filteredHistory = append(filteredHistory, item)
			}
		}
	} else {
		filteredHistory = allHistory
	}

	// --- Sort ---
	sort.Slice(filteredHistory, func(i, j int) bool {
		return filteredHistory[i].TanggalBeli.After(filteredHistory[j].TanggalBeli)
	})

	// --- Pagination ---
	page, _ := strconv.Atoi(c.QueryParam("page"))
	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}

	total := int64(len(filteredHistory))
	start := (page - 1) * limit
	end := start + limit
	if start > len(filteredHistory) {
		start = len(filteredHistory)
	}
	if end > len(filteredHistory) {
		end = len(filteredHistory)
	}

	paginatedHistory := filteredHistory[start:end]

	// --- Return Response ---
	return c.JSON(http.StatusOK, user.ServiceHistoryResponse{
		History: paginatedHistory,
		Page:    page,
		Limit:   limit,
		Total:   total,
	})
}
