package user

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"vpn-shop/backend-go/internal/infrastructure/database"
	"vpn-shop/backend-go/internal/infrastructure/http/middleware"
	models "vpn-shop/backend-go/internal/domain/entities/payment"
	"vpn-shop/backend-go/internal/domain/entities/shared"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// TransactionResponse adalah DTO untuk menampilkan data transaksi pengguna.
type TransactionResponse struct {
	ID          uint      `json:"id"`
	InvoiceID   string    `json:"invoice_id"`
	Description string    `json:"description"`
	Amount      int64     `json:"amount"`
	Type        string    `json:"type"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UserName    string    `json:"user_name,omitempty"`
	UserAvatar  *string   `json:"user_avatar,omitempty"`
}

// TransactionListResponse adalah respons untuk daftar transaksi dengan paginasi
type TransactionListResponse struct {
	Data       []TransactionResponse `json:"data"`
	Pagination shared.Pagination     `json:"pagination"`
}

// GetUserTransactions mengambil daftar transaksi pengguna yang sedang login.
// @Summary Get User Transactions
// @Description Mengambil daftar transaksi pengguna yang sedang login dengan paginasi dan filter.
// @Tags User
// @Security BearerAuth
// @Produce json
// @Param page query int false "Halaman" default(1)
// @Param limit query int false "Jumlah item per halaman" default(10)
// @Param search query string false "Kata kunci pencarian"
// @Param type query string false "Filter berdasarkan tipe transaksi (TOPUP, PURCHASE_MONTHLY, PURCHASE_HOURLY, TRIAL, RENEWAL, REFUND, BILLING, BILLED_HOURLY)"
// @Param status query string false "Filter berdasarkan status transaksi (PENDING, SUCCESS, FAILED)"
// @Success 200 {object} TransactionListResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me/transactions [get]
func GetUserTransactions(c echo.Context) error {
	// Ambil user ID dari token JWT
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Tidak dapat mengidentifikasi pengguna"})
	}

	// Parameter paginasi
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit

	// Parameter filter
	search := c.QueryParam("search")
	txType := strings.ToUpper(c.QueryParam("type"))
	txStatus := strings.ToUpper(c.QueryParam("status"))

	// Buat query dasar
	query := db.DB.Model(&models.Transaction{}).Preload("User").Where("user_id = ?", userID)

	// Tambahkan filter jika ada
	if search != "" {
		query = query.Where("description ILIKE ? OR invoice_id ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if txType != "" {
		query = query.Where("type = ?", txType)
	}

	if txStatus != "" {
		query = query.Where("status = ?", txStatus)
	}

	// Hitung total data untuk paginasi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menghitung total transaksi"})
	}

	// Ambil data transaksi dengan paginasi dan sorting
	var transactions []models.Transaction
	if err := query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&transactions).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data transaksi"})
	}

	// Transformasi data ke format respons
	responses := make([]TransactionResponse, len(transactions))
	for i, t := range transactions {
		responses[i] = TransactionResponse{
			ID:          t.ID,
			InvoiceID:   t.InvoiceID,
			Description: t.Description,
			Amount:      t.Amount,
			Type:        string(t.Type),
			Status:      string(t.Status),
			CreatedAt:   t.CreatedAt,
			UserName:    t.User.Name,
			UserAvatar:  t.User.PathPhoto,
		}
	}

	// Buat respons dengan paginasi
	pagination := shared.Pagination{
		Total:       total,
		PerPage:     int64(limit),
		CurrentPage: int64(page),
		LastPage:    (total + int64(limit) - 1) / int64(limit),
	}

	return c.JSON(http.StatusOK, TransactionListResponse{
		Data:       responses,
		Pagination: pagination,
	})
}

// GetUserTransactionByID mengambil detail transaksi pengguna berdasarkan ID.
// @Summary Get User Transaction By ID
// @Description Mengambil detail transaksi pengguna berdasarkan ID.
// @Tags User
// @Security BearerAuth
// @Produce json
// @Param id path int true "Transaction ID"
// @Success 200 {object} TransactionResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me/transactions/{id} [get]
func GetUserTransactionByID(c echo.Context) error {
	// Ambil user ID dari token JWT
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Tidak dapat mengidentifikasi pengguna"})
	}

	// Ambil ID transaksi dari parameter URL
	transactionID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "ID transaksi tidak valid"})
	}

	// Ambil data transaksi dari database
	var transaction models.Transaction
	result := db.DB.Preload("User").Where("id = ? AND user_id = ?", transactionID, userID).First(&transaction)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Transaksi tidak ditemukan"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data transaksi"})
	}

	// Transformasi data ke format respons
	response := TransactionResponse{
		ID:          transaction.ID,
		InvoiceID:   transaction.InvoiceID,
		Description: transaction.Description,
		Amount:      transaction.Amount,
		Type:        string(transaction.Type),
		Status:      string(transaction.Status),
		CreatedAt:   transaction.CreatedAt,
		UserName:    transaction.User.Name,
		UserAvatar:  transaction.User.PathPhoto,
	}

	return c.JSON(http.StatusOK, response)
}
