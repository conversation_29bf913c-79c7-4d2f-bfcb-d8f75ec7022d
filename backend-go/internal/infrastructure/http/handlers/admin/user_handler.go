package admin

import (
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"

	"vpn-shop/backend-go/internal/infrastructure/database"
	"vpn-shop/backend-go/internal/domain/entities/account"
	"vpn-shop/backend-go/internal/domain/entities/shared"
	userModel "vpn-shop/backend-go/internal/domain/entities/user"
)

// A simple role response
type RoleResponse struct {
	Name string `json:"name"`
}

// SwaggerUserDetailResponse is used to explicitly define the UserDetailResponse structure for Swagger
type SwaggerUserDetailResponse struct {
	ID               uint           `json:"id"`
	Username         string         `json:"username"`
	Name             string         `json:"name"`
	Email            string         `json:"email"`
	Saldo            int64          `json:"saldo"`
	Suspend          *string        `json:"suspend"`
	CreatedAt        time.Time      `json:"created_at"`
	EmailVerified    bool           `json:"email_verified"`
	Whatsapp         *string        `json:"whatsapp"`
	VerifWa          bool           `json:"verif_wa"`
	AccountType      string         `json:"account_type"`
	JumlahAkunTrial  int64          `json:"jumlah_akun_trial"`
	JumlahAkunHourly int64          `json:"jumlah_akun_hourly"`
	JumlahAkunMonth  int64          `json:"jumlah_akun_month"`
	JumlahAkunTrojan int64          `json:"jumlah_akun_trojan"`
	JumlahAkunVmess  int64          `json:"jumlah_akun_vmess"`
	JumlahAkunVless  int64          `json:"jumlah_akun_vless"`
	JumlahAkunSsh    int64          `json:"jumlah_akun_ssh"`
	TotalAccount     int64          `json:"total_account"`
	Roles            []RoleResponse `json:"roles"`
}

// A more detailed user response struct that matches frontend needs
type UserDetailResponse struct {
	ID               uint           `json:"id"`
	Username         string         `json:"username"`
	Name             string         `json:"name"`
	Email            string         `json:"email"`
	Saldo            int64          `json:"saldo"`
	Suspend          *string        `json:"suspend"`
	CreatedAt        time.Time      `json:"created_at"`
	EmailVerified    bool           `json:"email_verified"`
	Whatsapp         *string        `json:"whatsapp"`
	VerifWa          bool           `json:"verif_wa"`
	AccountType      string         `json:"account_type"`
	JumlahAkunTrial  int64          `json:"jumlah_akun_trial"`
	JumlahAkunHourly int64          `json:"jumlah_akun_hourly"`
	JumlahAkunMonth  int64          `json:"jumlah_akun_month"`
	JumlahAkunTrojan int64          `json:"jumlah_akun_trojan"`
	JumlahAkunVmess  int64          `json:"jumlah_akun_vmess"`
	JumlahAkunVless  int64          `json:"jumlah_akun_vless"`
	JumlahAkunSsh    int64          `json:"jumlah_akun_ssh"`
	TotalAccount     int64          `json:"total_account"`
	Roles            []RoleResponse `json:"roles"`
}

// SwaggerUserListResponse is used to explicitly define the UserListResponse structure for Swagger
type SwaggerUserListResponse struct {
	Users []SwaggerUserDetailResponse `json:"users"`
}

// UserListResponse for returning a list of users
type UserListResponse struct {
	Users []UserDetailResponse `json:"users"`
}

// GetAllUsers retrieves all users. Admin only.
// @Summary Get all users
// @Description Get a list of all users
// @Tags Admin
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} SwaggerUserListResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Router /admin/users [get]
func GetAllUsers(c echo.Context) error {
	var users []userModel.User
	if result := db.DB.Preload("Roles").Find(&users); result.Error != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Tidak dapat mengambil daftar pengguna"})
	}

	var responseUsers []UserDetailResponse
	for _, user := range users {
		userIDStr := strconv.Itoa(int(user.ID))

		// Count accounts for each protocol type
		var totalTrojan, totalVmess, totalVless, totalSsh int64
		db.DB.Model(&account.AccountTrojan{}).Where("user_id = ?", userIDStr).Count(&totalTrojan)
		db.DB.Model(&account.AccountVmess{}).Where("user_id = ?", userIDStr).Count(&totalVmess)
		db.DB.Model(&account.AccountVless{}).Where("user_id = ?", userIDStr).Count(&totalVless)
		db.DB.Model(&account.AccountSsh{}).Where("user_id = ?", userIDStr).Count(&totalSsh)

		// Count accounts by subscription type (trial, month, hourly)
		var countMonth, countTrial, countHourly int64
		var tempCount int64
		modelsToCount := []interface{}{&account.AccountTrojan{}, &account.AccountVmess{}, &account.AccountVless{}, &account.AccountSsh{}}

		for _, model := range modelsToCount {
			tempCount = 0
			db.DB.Model(model).Where("user_id = ? AND subscription_type = ?", userIDStr, "month").Count(&tempCount)
			countMonth += tempCount

			tempCount = 0
			db.DB.Model(model).Where("user_id = ? AND subscription_type = ?", userIDStr, "trial").Count(&tempCount)
			countTrial += tempCount

			tempCount = 0
			db.DB.Model(model).Where("user_id = ? AND is_hourly = ?", userIDStr, true).Count(&tempCount)
			countHourly += tempCount
		}

		// Determine account type and roles
		accountType := "Member"
		roles := []RoleResponse{}
		for _, role := range user.Roles {
			roles = append(roles, RoleResponse{Name: role.Name})
			switch role.Name {
			case "admin":
				accountType = "Admin"
			case "reseller":
				accountType = "Reseller"
			}
		}

		// Ensure email is not null
		var email string
		if user.Email != nil {
			email = *user.Email
		}

		responseUsers = append(responseUsers, UserDetailResponse{
			ID:               user.ID,
			Username:         user.Username,
			Name:             user.Name,
			Email:            email,
			Saldo:            user.Saldo,
			Suspend:          user.Suspend,
			CreatedAt:        user.CreatedAt,
			EmailVerified:    user.EmailVerified == 1,
			Whatsapp:         user.Whatsapp,
			VerifWa:          user.VerifWa == 1,
			AccountType:      accountType,
			JumlahAkunTrial:  countTrial,
			JumlahAkunHourly: countHourly,
			JumlahAkunMonth:  countMonth,
			JumlahAkunTrojan: totalTrojan,
			JumlahAkunVmess:  totalVmess,
			JumlahAkunVless:  totalVless,
			JumlahAkunSsh:    totalSsh,
			TotalAccount:     totalTrojan + totalVmess + totalVless + totalSsh,
			Roles:            roles,
		})
	}

	return c.JSON(http.StatusOK, UserListResponse{Users: responseUsers})
}

// UpdateUserRequest defines the request body for updating a user by an admin.
type UpdateUserRequest struct {
	Name     *string  `json:"name"`
	Email    *string  `json:"email"`
	Saldo    *int64   `json:"saldo"`
	Whatsapp *string  `json:"whatsapp"`
	Roles    []string `json:"roles"` // Admin can update roles
}

// UpdateUser updates a user's details. Admin only.
// @Summary Update a user
// @Description Update a user's details by their ID
// @Tags Admin
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path int true "User ID"
// @Param user body UpdateUserRequest true "User data to update"
// @Success 200 {object} SwaggerUserDetailResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Router /admin/users/{id} [put]
func UpdateUser(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "ID pengguna tidak valid"})
	}

	var user userModel.User
	if result := db.DB.Preload("Roles").First(&user, id); result.Error != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
	}

	var req UpdateUserRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	// Validate email format if provided
	if req.Email != nil {
		emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
		if !emailRegex.MatchString(*req.Email) {
			return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format email tidak valid"})
		}

		// Check if email is already taken by another user
		var existingUser userModel.User
		if err := db.DB.Where("email = ? AND id != ?", *req.Email, id).First(&existingUser).Error; err == nil {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Email sudah digunakan oleh akun lain"})
		}
		user.Email = req.Email
	}

	if req.Name != nil {
		user.Name = *req.Name
	}
	if req.Saldo != nil {
		user.Saldo = *req.Saldo
	}
	if req.Whatsapp != nil {
		user.Whatsapp = req.Whatsapp
	}

	if result := db.DB.Save(&user); result.Error != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui detail dasar pengguna"})
	}

	// Handle role updates if they are provided in the request
	if req.Roles != nil {
		var newRoles []*userModel.Role
		// Find role objects from the database based on the names provided
		if err := db.DB.Where("name IN ?", req.Roles).Find(&newRoles).Error; err != nil {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Terjadi kesalahan database saat mengambil data peran"})
		}

		// Ensure all requested roles are valid and exist in the database
		if len(newRoles) != len(req.Roles) {
			// For a better error message, find out which role is invalid
			foundRolesMap := make(map[string]bool)
			for _, role := range newRoles {
				foundRolesMap[role.Name] = true
			}
			for _, requestedRole := range req.Roles {
				if !foundRolesMap[requestedRole] {
					return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: fmt.Sprintf("Peran '%s' tidak valid", requestedRole)})
				}
			}
		}

		// Atomically replace the user's current roles with the new set
		if err := db.DB.Model(&user).Association("Roles").Replace(newRoles); err != nil {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui peran pengguna"})
		}
	}

	// We must reload the user from the DB to get the updated roles for the response
	if result := db.DB.Preload("Roles").First(&user, id); result.Error != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Tidak dapat mengambil data pengguna setelah pembaruan"})
	}

	userIDStr := strconv.Itoa(int(user.ID))

	// Count accounts for each protocol type
	var totalTrojan, totalVmess, totalVless, totalSsh int64
	db.DB.Model(&account.AccountTrojan{}).Where("user_id = ?", userIDStr).Count(&totalTrojan)
	db.DB.Model(&account.AccountVmess{}).Where("user_id = ?", userIDStr).Count(&totalVmess)
	db.DB.Model(&account.AccountVless{}).Where("user_id = ?", userIDStr).Count(&totalVless)
	db.DB.Model(&account.AccountSsh{}).Where("user_id = ?", userIDStr).Count(&totalSsh)

	// Count accounts by subscription type (trial, month, hourly)
	var countMonth, countTrial, countHourly int64
	var tempCount int64
	modelsToCount := []interface{}{&account.AccountTrojan{}, &account.AccountVmess{}, &account.AccountVless{}, &account.AccountSsh{}}

	for _, model := range modelsToCount {
		tempCount = 0
		db.DB.Model(model).Where("user_id = ? AND subscription_type = ?", userIDStr, "month").Count(&tempCount)
		countMonth += tempCount

		tempCount = 0
		db.DB.Model(model).Where("user_id = ? AND subscription_type = ?", userIDStr, "trial").Count(&tempCount)
		countTrial += tempCount

		tempCount = 0
		db.DB.Model(model).Where("user_id = ? AND is_hourly = ?", userIDStr, true).Count(&tempCount)
		countHourly += tempCount
	}

	// Determine account type and build roles response
	accountType := "Member"
	roles := []RoleResponse{}
	if user.Roles != nil {
		for _, role := range user.Roles {
			if role != nil {
				roles = append(roles, RoleResponse{Name: role.Name})
				switch role.Name {
				case "admin":
					accountType = "Admin"
				case "reseller":
					accountType = "Reseller"
				}
			}
		}
	}

	// Ensure email is not null
	email := ""
	if user.Email != nil {
		email = *user.Email
	}

	response := UserDetailResponse{
		ID:               user.ID,
		Username:         user.Username,
		Name:             user.Name,
		Email:            email,
		Saldo:            user.Saldo,
		Suspend:          user.Suspend,
		CreatedAt:        user.CreatedAt,
		EmailVerified:    user.EmailVerified == 1,
		Whatsapp:         user.Whatsapp,
		VerifWa:          user.VerifWa == 1,
		AccountType:      accountType,
		JumlahAkunTrial:  countTrial,
		JumlahAkunHourly: countHourly,
		JumlahAkunMonth:  countMonth,
		JumlahAkunTrojan: totalTrojan,
		JumlahAkunVmess:  totalVmess,
		JumlahAkunVless:  totalVless,
		JumlahAkunSsh:    totalSsh,
		TotalAccount:     totalTrojan + totalVmess + totalVless + totalSsh,
		Roles:            roles,
	}

	return c.JSON(http.StatusOK, response)
}

// DeleteUser soft deletes a user. Admin only.
// @Summary Delete a user
// @Description Soft delete a user by their ID
// @Tags Admin
// @Security BearerAuth
// @Produce  json
// @Param id path int true "User ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 403 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Router /admin/users/{id} [delete]
func DeleteUser(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "ID pengguna tidak valid"})
	}

	var user userModel.User
	if result := db.DB.First(&user, id); result.Error != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
	}

	if result := db.DB.Delete(&user); result.Error != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menghapus pengguna"})
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Pengguna berhasil dihapus"})
}
