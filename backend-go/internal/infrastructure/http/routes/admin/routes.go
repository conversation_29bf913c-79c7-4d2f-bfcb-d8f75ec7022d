package admin

import (
	"vpn-shop/backend-go/internal/infrastructure/http/handlers/admin"
	"vpn-shop/backend-go/internal/infrastructure/http/middleware"

	"github.com/labstack/echo/v4"
)

// SetupAdminRoutes sets up all routes for admin-specific functionality.
func SetupAdminRoutes(e *echo.Group) {
	// All routes in this group are prefixed with /admin and protected by AdminMiddleware
	e.Use(middleware.AdminMiddleware)

	// Settings routes
	// GET /api/v1/admin/settings
	e.GET("/settings", admin.GetAdminSettings)
	// PUT /api/v1/admin/settings
	e.PUT("/settings", admin.UpdateAdminSettings)

	// Invoice routes removed

	// User management routes
	usersGroup := e.Group("/users")
	usersGroup.GET("", admin.GetAllUsers)
	usersGroup.PUT("/:id", admin.UpdateUser)
	usersGroup.DELETE("/:id", admin.DeleteUser)

	// Announcement routes
	announcementsGroup := e.Group("/announcements")
	announcementsGroup.POST("", admin.CreateAnnouncement)
	announcementsGroup.PUT("/:id", admin.UpdateAnnouncement)
	announcementsGroup.DELETE("/:id", admin.DeleteAnnouncement)
}
