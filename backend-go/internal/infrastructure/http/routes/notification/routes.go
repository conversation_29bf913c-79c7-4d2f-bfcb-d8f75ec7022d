package notification

import (
	"vpn-shop/backend-go/internal/infrastructure/http/handlers/notification"

	"github.com/labstack/echo/v4"
)

// SetupNotificationRoutes registers notification routes
func SetupNotificationRoutes(e *echo.Group) {
	// Get user notifications with pagination
	e.GET("", notification.GetUserNotifications)

	// Get notification statistics (unread count, total count)
	e.GET("/stats", notification.GetNotificationStats)

	// Mark specific notifications as read
	e.POST("/mark-read", notification.MarkNotificationsAsRead)

	// Mark all notifications as read
	e.POST("/mark-all-read", notification.MarkAllNotificationsAsRead)

	// Delete specific notification
	e.DELETE("/:id", notification.DeleteNotification)
}
