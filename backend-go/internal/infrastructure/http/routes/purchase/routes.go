package purchase

import (
	"vpn-shop/backend-go/internal/infrastructure/http/handlers/purchase"

	"github.com/labstack/echo/v4"
)

// SetupPurchaseRoutes configures the routes for creating new accounts.
func SetupPurchaseRoutes(group *echo.Group) {
	group.POST("/trial", purchase.PurchaseTrialAccount)
	group.POST("/monthly", purchase.PurchaseMonthlyAccount)
	group.POST("/hourly", purchase.PurchaseHourlyAccount)
}
