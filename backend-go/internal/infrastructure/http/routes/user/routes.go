package user

import (
	"vpn-shop/backend-go/internal/infrastructure/http/handlers/user"

	"github.com/labstack/echo/v4"
)

func SetupUserRoutes(e *echo.Group) {
	e.GET("/me", user.GetMe)
	e.PUT("/me", user.UpdateMe)
	e.PUT("/me/change-password", user.ChangePassword)
	e.POST("/me/photo", user.UploadPhoto)
	e.GET("/me/stats", user.GetUserStats)

	// User Invoices
	e.GET("/me/invoices", user.GetUserInvoices)
	e.GET("/me/invoices/:invoice_id", user.GetUserInvoiceDetails)

	// User Transactions
	e.GET("/me/transactions", user.GetUserTransactions)
	e.GET("/me/transactions/:id", user.GetUserTransactionByID)
}
