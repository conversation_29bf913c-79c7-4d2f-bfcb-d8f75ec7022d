package payment

import (
	payment_handler "vpn-shop/backend-go/internal/infrastructure/http/handlers/payment"

	"github.com/labstack/echo/v4"
)

// TripayCallbackHandler is re-exported from the handler package to be accessible by main.go
// for setting up the unauthenticated callback route.
var TripayCallbackHandler = payment_handler.TripayCallbackHandler

// SetupPaymentRoutes configures the authenticated routes for payment.
func SetupPaymentRoutes(e *echo.Group) {
	// Endpoint ini untuk membuat transaksi baru, memerlukan otentikasi.
	e.POST("/create", payment_handler.CreatePaymentHandler)

	// Endpoint untuk top-up saldo, memerlukan otentikasi.
	e.POST("/topup", payment_handler.TopUpHandler)

	// Endpoint untuk mendapatkan daftar channel pembayaran yang tersedia.
	e.GET("/channels", payment_handler.GetPaymentChannelsHandler)

	// Endpoint untuk mengecek status pembayaran dari Tripay.
	e.GET("/status/:reference", payment_handler.CheckPaymentStatusHandler)
}
