package middleware

import (
	"net/http"
	"vpn-shop/backend-go/internal/infrastructure/database"
	"vpn-shop/backend-go/internal/domain/entities/shared"
	user "vpn-shop/backend-go/internal/domain/entities/user"

	"github.com/labstack/echo/v4"
)

func AdminMiddleware(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		userID, err := GetUserIDFromToken(c)
		if err != nil {
			return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
		}

		var user user.User
		if err := db.DB.Preload("Roles").First(&user, userID).Error; err != nil {
			return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Akses ditolak: Pengguna tidak ditemukan"})
		}

		hasAdminRole := false
		for _, role := range user.Roles {
			if role.Name == "admin" {
				hasAdminRole = true
				break
			}
		}

		if !hasAdminRole {
			return c.JSON(http.StatusForbidden, shared.ErrorResponse{Error: "Akses dilarang: Diperlukan akses admin"})
		}

		return next(c)
	}
}
