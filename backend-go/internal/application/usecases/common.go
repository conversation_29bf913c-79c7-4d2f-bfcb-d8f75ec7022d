package services

// MarzbanUserRequest defines the universal payload for creating or updating a user in Marzban.
// It accommodates both monthly (with expire) and hourly (without expire) accounts.
type MarzbanUserRequest struct {
	Username               string                 `json:"username"`
	Note                   string                 `json:"note,omitempty"`
	Proxies                map[string]interface{} `json:"proxies"`
	Inbounds               map[string][]string    `json:"inbounds,omitempty"`
	Expire                 int64                  `json:"expire,omitempty"`
	DataLimit              int64                  `json:"data_limit,omitempty"`
	DataLimitResetStrategy string                 `json:"data_limit_reset_strategy,omitempty"`
	Status                 string                 `json:"status"`
}
