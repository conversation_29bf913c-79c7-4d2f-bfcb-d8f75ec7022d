package services

import (
	"errors"
	"vpn-shop/backend-go/internal/infrastructure/database"
	"vpn-shop/backend-go/internal/domain/entities/account"
	"vpn-shop/backend-go/internal/domain/entities/server"

	"gorm.io/gorm"
)

// FindAccountByUsernameAndServer searches for an account across all relevant tables
// (trojan, vmess, vless) using the username and server code.
// It returns a generic AnyAccount struct if found, otherwise returns an error.
func FindAccountByUsernameAndServer(username string, serverCode string) (*account.AnyAccount, error) {
	db := db.DB
	var server server.Server
	// The server model uses 'Kode' for the server code.
	if err := db.Where("kode = ?", serverCode).First(&server).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("server not found")
		}
		return nil, err
	}

	// Search in AccountTrojan
	var trojanAccount account.AccountTrojan
	// The account models use 'KodeServer'
	if err := db.Where("username = ? AND kode_server = ?", username, serverCode).First(&trojanAccount).Error; err == nil {
		return &account.AnyAccount{
			ID:          trojanAccount.AccountID,
			Username:    trojanAccount.Username,
			Password:    trojanAccount.UUID, // Trojan uses UUID
			AccountType: "trojan",
			Status:      trojanAccount.Status,
			ExpiredDate: trojanAccount.Expired,
			ServerID:    server.ServerID,
			UserID:      trojanAccount.UserID,
			Server:      server,
			CreatedAt:   trojanAccount.CreatedAt,
			UpdatedAt:   trojanAccount.UpdatedAt,
			DeletedAt:   trojanAccount.DeletedAt,
		}, nil
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err // Return actual db error
	}

	// Search in AccountVmess
	var vmessAccount account.AccountVmess
	if err := db.Where("username = ? AND kode_server = ?", username, serverCode).First(&vmessAccount).Error; err == nil {
		return &account.AnyAccount{
			ID:          vmessAccount.AccountID,
			Username:    vmessAccount.Username,
			Password:    vmessAccount.UUID, // Vmess uses UUID
			AccountType: "vmess",
			Status:      vmessAccount.Status,
			ExpiredDate: vmessAccount.Expired,
			ServerID:    server.ServerID,
			UserID:      vmessAccount.UserID,
			Server:      server,
			CreatedAt:   vmessAccount.CreatedAt,
			UpdatedAt:   vmessAccount.UpdatedAt,
			DeletedAt:   vmessAccount.DeletedAt,
		}, nil
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err // Return actual db error
	}

	// Search in AccountVless
	var vlessAccount account.AccountVless
	if err := db.Where("username = ? AND kode_server = ?", username, serverCode).First(&vlessAccount).Error; err == nil {
		return &account.AnyAccount{
			ID:          vlessAccount.AccountID,
			Username:    vlessAccount.Username,
			Password:    vlessAccount.UUID, // Vless uses UUID
			AccountType: "vless",
			Status:      vlessAccount.Status,
			ExpiredDate: vlessAccount.Expired,
			ServerID:    server.ServerID,
			UserID:      vlessAccount.UserID,
			Server:      server,
			CreatedAt:   vlessAccount.CreatedAt,
			UpdatedAt:   vlessAccount.UpdatedAt,
			DeletedAt:   vlessAccount.DeletedAt,
		}, nil
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err // Return actual db error
	}

	// If not found in any table
	return nil, gorm.ErrRecordNotFound
}
