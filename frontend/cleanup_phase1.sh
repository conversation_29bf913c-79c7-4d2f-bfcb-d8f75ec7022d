#!/bin/bash

echo "🧹 FRONTEND CLEANUP - FASE 1: Hapus File yang Pasti Tidak Dipakai"
echo "=================================================================="

# Backup dulu
echo "📦 Creating backup..."
cp -r src src_backup_$(date +%Y%m%d_%H%M%S)
echo "✅ Backup created: src_backup_$(date +%Y%m%d_%H%M%S)"

echo ""
echo "🗑️ Removing safe-to-delete files..."

# Hapus demo JavaScript
if [ -f "src/js/us-aea-en.js" ]; then
    rm src/js/us-aea-en.js
    echo "✅ Removed: src/js/us-aea-en.js"
fi

# Hapus country utils yang tidak dipakai
if [ -f "src/utils/country.ts" ]; then
    rm src/utils/country.ts
    echo "✅ Removed: src/utils/country.ts"
fi

# Hapus CalenderBox component
if [ -d "src/components/CalenderBox" ]; then
    rm -rf src/components/CalenderBox
    echo "✅ Removed: src/components/CalenderBox/"
fi

# Hapus demo pages (tapi keep yang dipakai)
echo ""
echo "🗑️ Removing demo pages..."

# Hapus charts demo pages (tapi keep components yang dipakai)
if [ -d "src/app/(main)/charts" ]; then
    rm -rf src/app/(main)/charts
    echo "✅ Removed: src/app/(main)/charts/"
fi

# Hapus forms demo pages
if [ -d "src/app/(main)/forms" ]; then
    rm -rf src/app/(main)/forms
    echo "✅ Removed: src/app/(main)/forms/"
fi

# Hapus tables demo pages
if [ -d "src/app/(main)/tables" ]; then
    rm -rf src/app/(main)/tables
    echo "✅ Removed: src/app/(main)/tables/"
fi

# Hapus ui-elements demo pages (tapi keep components yang dipakai di product)
if [ -d "src/app/(main)/ui-elements" ]; then
    rm -rf src/app/(main)/ui-elements
    echo "✅ Removed: src/app/(main)/ui-elements/"
fi

# Hapus pages demo
if [ -d "src/app/(main)/pages" ]; then
    rm -rf src/app/(main)/pages
    echo "✅ Removed: src/app/(main)/pages/"
fi

echo ""
echo "🧹 Cleaning up demo components (yang tidak dipakai)..."

# Hapus demo tables yang tidak dipakai
if [ -d "src/components/Tables/dashboard-transactions" ]; then
    rm -rf src/components/Tables/dashboard-transactions
    echo "✅ Removed: src/components/Tables/dashboard-transactions/"
fi

if [ -d "src/components/Tables/top-channels" ]; then
    rm -rf src/components/Tables/top-channels
    echo "✅ Removed: src/components/Tables/top-channels/"
fi

if [ -d "src/components/Tables/top-products" ]; then
    rm -rf src/components/Tables/top-products
    echo "✅ Removed: src/components/Tables/top-products/"
fi

echo ""
echo "✅ FASE 1 SELESAI!"
echo ""
echo "📋 YANG SUDAH DIHAPUS:"
echo "- Demo JavaScript files"
echo "- Unused utility files"
echo "- Demo pages (charts, forms, tables, ui-elements, pages)"
echo "- Demo table components"
echo "- Calendar component"
echo ""
echo "⚠️  NEXT STEPS:"
echo "1. Test aplikasi untuk memastikan tidak ada yang rusak"
echo "2. Jalankan 'npm run dev' dan cek semua halaman"
echo "3. Jika semua OK, lanjut ke Fase 2"
echo ""
echo "🔄 To test: npm run dev"
