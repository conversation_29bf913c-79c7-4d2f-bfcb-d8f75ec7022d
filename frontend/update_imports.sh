#!/bin/bash

echo "🔄 UPDATING IMPORT PATHS AFTER REORGANIZATION"
echo "============================================="

# Function to update imports in files
update_imports() {
    echo "📝 Updating import paths..."
    
    # Find all TypeScript/JavaScript files and update imports
    find src/ -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | while read -r file; do
        # Update component imports
        sed -i 's|@/components/Auth/|@/components/auth/|g' "$file"
        sed -i 's|@/components/Layouts/|@/components/layout/|g' "$file"
        sed -i 's|@/components/Dashboard/|@/components/dashboard/|g' "$file"
        sed -i 's|@/components/Tables/|@/components/tables/|g' "$file"
        sed -i 's|@/components/TopUp/|@/components/billing/|g' "$file"
        
        # Update page imports
        sed -i 's|/product/|/products/|g' "$file"
        sed -i 's|/detail/|/account/|g' "$file"
        sed -i 's|/top-up|/billing/top-up|g' "$file"
        sed -i 's|/transactions|/billing/transactions|g' "$file"
        sed -i 's|/history|/billing/history|g' "$file"
        
        # Update CSS imports
        sed -i 's|@/css/|@/styles/|g' "$file"
        sed -i 's|from.*css/|from "@/styles/|g' "$file"
        
        # Update relative imports for moved files
        sed -i 's|../../../components/Auth/|../../../components/auth/|g' "$file"
        sed -i 's|../../../components/Layouts/|../../../components/layout/|g' "$file"
        sed -i 's|../../../components/Dashboard/|../../../components/dashboard/|g' "$file"
        sed -i 's|../../../components/Tables/|../../../components/tables/|g' "$file"
        sed -i 's|../../../components/TopUp/|../../../components/billing/|g' "$file"
    done
    
    echo "✅ Import paths updated"
}

# Function to update routing
update_routing() {
    echo "🛣️ Updating routing configurations..."
    
    # Update any routing configurations
    find src/ -name "*.ts" -o -name "*.tsx" | xargs grep -l "href\|router\|Link" | while read -r file; do
        # Update href paths
        sed -i 's|href="/product/|href="/products/|g' "$file"
        sed -i 's|href="/detail/|href="/account/|g' "$file"
        sed -i 's|href="/top-up|href="/billing/top-up|g' "$file"
        sed -i 's|href="/transactions|href="/billing/transactions|g' "$file"
        sed -i 's|href="/history|href="/billing/history|g' "$file"
        
        # Update router.push paths
        sed -i 's|router.push("/product/|router.push("/products/|g' "$file"
        sed -i 's|router.push("/detail/|router.push("/account/|g' "$file"
        sed -i 's|router.push("/top-up|router.push("/billing/top-up|g' "$file"
        sed -i 's|router.push("/transactions|router.push("/billing/transactions|g' "$file"
        sed -i 's|router.push("/history|router.push("/billing/history|g' "$file"
    done
    
    echo "✅ Routing updated"
}

# Function to update middleware and configs
update_configs() {
    echo "⚙️ Updating configurations..."
    
    # Update middleware.ts if exists
    if [ -f "src/middleware.ts" ]; then
        sed -i 's|/product/|/products/|g' src/middleware.ts
        sed -i 's|/detail/|/account/|g' src/middleware.ts
        sed -i 's|/top-up|/billing/top-up|g' src/middleware.ts
        sed -i 's|/transactions|/billing/transactions|g' src/middleware.ts
        sed -i 's|/history|/billing/history|g' src/middleware.ts
        echo "✅ Middleware updated"
    fi
    
    # Update any config files
    find . -name "*.config.*" | while read -r file; do
        sed -i 's|/css/|/styles/|g' "$file"
    done
    
    echo "✅ Configurations updated"
}

# Run updates
update_imports
update_routing
update_configs

echo ""
echo "🔍 Checking for remaining issues..."

# Check for potential broken imports
echo "🔍 Checking for potential broken imports..."
broken_imports=$(find src/ -name "*.ts" -o -name "*.tsx" | xargs grep -n "from.*\.\./.*" | grep -E "(Auth|Layouts|Dashboard|Tables|TopUp|css)" | head -10)

if [ -n "$broken_imports" ]; then
    echo "⚠️  Found potential broken imports:"
    echo "$broken_imports"
    echo ""
    echo "Please review and fix these manually."
else
    echo "✅ No obvious broken imports found"
fi

echo ""
echo "✅ IMPORT PATHS UPDATE COMPLETED!"
echo ""
echo "📋 WHAT WAS UPDATED:"
echo "- Component import paths"
echo "- Page routing paths"
echo "- CSS import paths"
echo "- href and router.push paths"
echo "- Configuration files"
echo ""
echo "🧪 TESTING REQUIRED:"
echo "1. Run 'npm run dev'"
echo "2. Check browser console for errors"
echo "3. Navigate through all pages"
echo "4. Test all functionality"
echo ""
echo "🔧 If you find issues:"
echo "1. Check the broken imports listed above"
echo "2. Manually fix any remaining path issues"
echo "3. Use IDE's 'Find and Replace' for specific patterns"
