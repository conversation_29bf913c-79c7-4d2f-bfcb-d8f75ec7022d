{"c": ["app/layout", "app/(main)/dashboard/page", "webpack"], "r": ["app/(dashboard)/notifications/page"], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2F(dashboard)%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/(dashboard)/notifications/page.tsx"]}