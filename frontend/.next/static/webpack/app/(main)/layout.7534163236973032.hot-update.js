"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/Layouts/sidebar/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/Layouts/sidebar/index.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_logo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/logo */ \"(app-pages-browser)/./src/components/logo.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _data__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./data */ \"(app-pages-browser)/./src/components/Layouts/sidebar/data/index.ts\");\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons */ \"(app-pages-browser)/./src/components/Layouts/sidebar/icons.tsx\");\n/* harmony import */ var _menu_item__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./menu-item */ \"(app-pages-browser)/./src/components/Layouts/sidebar/menu-item.tsx\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _sidebar_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./sidebar-context */ \"(app-pages-browser)/./src/components/Layouts/sidebar/sidebar-context.tsx\");\n/* harmony import */ var _contexts_BalanceContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/BalanceContext */ \"(app-pages-browser)/./src/contexts/BalanceContext.tsx\");\n/* harmony import */ var _header_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../header/icons */ \"(app-pages-browser)/./src/components/Layouts/header/icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Sidebar() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_9__.useSession)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { setIsOpen, isOpen, isMobile, toggleSidebar } = (0,_sidebar_context__WEBPACK_IMPORTED_MODULE_10__.useSidebarContext)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const { balance, isLoading } = (0,_contexts_BalanceContext__WEBPACK_IMPORTED_MODULE_11__.useBalance)();\n    const toggleExpanded = (title)=>{\n        setExpandedItems((prev)=>prev.includes(title) ? [] : [\n                title\n            ]);\n    // Uncomment the following line to enable multiple expanded items\n    // setExpandedItems((prev) =>\n    //   prev.includes(title) ? prev.filter((t) => t !== title) : [...prev, title],\n    // );\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            // Keep collapsible open, when it's subpage is active\n            _data__WEBPACK_IMPORTED_MODULE_6__.NAV_DATA.some({\n                \"Sidebar.useEffect\": (section)=>{\n                    return section.items.some({\n                        \"Sidebar.useEffect\": (item)=>{\n                            return item.items && item.items.some({\n                                \"Sidebar.useEffect\": (subItem)=>{\n                                    if (subItem.url === pathname) {\n                                        if (!expandedItems.includes(item.title)) {\n                                            toggleExpanded(item.title);\n                                        }\n                                        // Break the loop\n                                        return true;\n                                    }\n                                }\n                            }[\"Sidebar.useEffect\"]);\n                        }\n                    }[\"Sidebar.useEffect\"]);\n                }\n            }[\"Sidebar.useEffect\"]);\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isMobile && isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black/50 transition-opacity duration-300\",\n                onClick: ()=>setIsOpen(false),\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"max-w-[290px] overflow-hidden border-r border-gray-200 bg-white transition-[width] duration-200 ease-linear dark:border-gray-800 dark:bg-gray-dark\", isMobile ? \"fixed bottom-0 top-0 z-50\" : \"sticky top-0 h-screen\", isOpen ? \"w-full\" : \"w-0\"),\n                \"aria-label\": \"Main navigation\",\n                \"aria-hidden\": !isOpen,\n                inert: !isOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-full flex-col py-10 pl-[25px] pr-[7px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative pr-4.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    onClick: ()=>isMobile && toggleSidebar(),\n                                    className: \"px-0 py-2.5 min-[850px]:py-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_logo__WEBPACK_IMPORTED_MODULE_1__.Logo, {}, void 0, false, {\n                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleSidebar,\n                                    className: \"absolute left-3/4 right-4.5 top-1/2 -translate-y-1/2 text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Close Menu\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_7__.ArrowLeftIcon, {\n                                            className: \"ml-auto size-7\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 mb-3 mx-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 rounded-md bg-primary/10 px-2.5 py-1.5 dark:bg-primary/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header_icons__WEBPACK_IMPORTED_MODULE_12__.WalletIcon, {\n                                        className: \"h-4 w-4 text-primary flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium text-dark dark:text-white text-xs truncate\",\n                                        children: isLoading ? \"Memuat...\" : balance !== null ? new Intl.NumberFormat('id-ID', {\n                                            style: 'currency',\n                                            currency: 'IDR',\n                                            minimumFractionDigits: 0,\n                                            maximumFractionDigits: 0\n                                        }).format(balance) : \"Rp 0\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"custom-scrollbar mt-6 flex-1 overflow-y-auto pr-3 min-[850px]:mt-10\",\n                            children: _data__WEBPACK_IMPORTED_MODULE_6__.NAV_DATA.map((section)=>{\n                                var _session_user_roles, _session_user;\n                                const isAdmin = session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : (_session_user_roles = _session_user.roles) === null || _session_user_roles === void 0 ? void 0 : _session_user_roles.includes(\"admin\");\n                                const filteredItems = section.items.filter((item)=>!item.adminOnly || isAdmin);\n                                if (filteredItems.length === 0) {\n                                    return null;\n                                }\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"mb-5 text-sm font-medium text-dark-4 dark:text-dark-6\",\n                                            children: section.label\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            role: \"navigation\",\n                                            \"aria-label\": section.label,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: filteredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: item.items && item.items.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_item__WEBPACK_IMPORTED_MODULE_8__.MenuItem, {\n                                                                    isActive: item.items.some((param)=>{\n                                                                        let { url } = param;\n                                                                        return url === pathname;\n                                                                    }),\n                                                                    onClick: ()=>toggleExpanded(item.title),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                            className: \"size-6 shrink-0\",\n                                                                            \"aria-hidden\": \"true\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                            lineNumber: 140,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: item.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                            lineNumber: 144,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_7__.ChevronUp, {\n                                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto rotate-180 transition-transform duration-200\", expandedItems.includes(item.title) && \"rotate-0\"),\n                                                                            \"aria-hidden\": \"true\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                            lineNumber: 145,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                expandedItems.includes(item.title) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"ml-9 mr-0 space-y-1.5 pb-[15px] pr-0 pt-2\",\n                                                                    role: \"menu\",\n                                                                    children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            role: \"none\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_item__WEBPACK_IMPORTED_MODULE_8__.MenuItem, {\n                                                                                as: \"link\",\n                                                                                href: subItem.url,\n                                                                                isActive: pathname === subItem.url,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: subItem.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                                    lineNumber: 167,\n                                                                                    columnNumber: 41\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                                lineNumber: 162,\n                                                                                columnNumber: 39\n                                                                            }, this)\n                                                                        }, subItem.title, false, {\n                                                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                            lineNumber: 161,\n                                                                            columnNumber: 37\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 29\n                                                        }, this) : (()=>{\n                                                            const href = \"url\" in item ? item.url : \"/\" + item.title.toLowerCase().split(\" \").join(\"-\");\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_item__WEBPACK_IMPORTED_MODULE_8__.MenuItem, {\n                                                                className: \"flex items-center gap-3 py-3\",\n                                                                as: \"link\",\n                                                                href: href,\n                                                                isActive: pathname === href,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                        className: \"size-6 shrink-0\",\n                                                                        \"aria-hidden\": \"true\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 35\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 33\n                                                            }, this);\n                                                        })()\n                                                    }, item.title, false, {\n                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, section.label, true, {\n                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/index.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"crYzW3zuiIKKEL9FqxZs6h+S3Hg=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_9__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        _sidebar_context__WEBPACK_IMPORTED_MODULE_10__.useSidebarContext,\n        _contexts_BalanceContext__WEBPACK_IMPORTED_MODULE_11__.useBalance\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Layouts/sidebar/index.tsx\n"));

/***/ })

});