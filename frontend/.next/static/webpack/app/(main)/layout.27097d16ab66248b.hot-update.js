"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/logo.tsx":
/*!*********************************!*\
  !*** ./src/components/logo.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InsomVPNLogo: () => (/* reexport safe */ _logo_InsomVPNLogo__WEBPACK_IMPORTED_MODULE_1__.InsomVPNLogo),\n/* harmony export */   Logo: () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _logo_InsomVPNLogo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logo/InsomVPNLogo */ \"(app-pages-browser)/./src/components/logo/InsomVPNLogo.tsx\");\n\n\nfunction Logo() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logo_InsomVPNLogo__WEBPACK_IMPORTED_MODULE_1__.InsomVPNLogo, {\n        variant: \"full\",\n        size: \"lg\"\n    }, void 0, false, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n_c = Logo;\n// Export the main logo component for other uses\n\nvar _c;\n$RefreshReg$(_c, \"Logo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xvZ28udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW1EO0FBRTVDLFNBQVNDO0lBQ2QscUJBQU8sOERBQUNELDREQUFZQTtRQUFDRSxTQUFRO1FBQU9DLE1BQUs7Ozs7OztBQUMzQztLQUZnQkY7QUFJaEIsZ0RBQWdEO0FBQ0ciLCJzb3VyY2VzIjpbIi9ob21lL2luc29tbmlhL3Byb2plY3QvdnBuLXNob3AvZnJvbmRlbmQvc3JjL2NvbXBvbmVudHMvbG9nby50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW5zb21WUE5Mb2dvIH0gZnJvbSBcIi4vbG9nby9JbnNvbVZQTkxvZ29cIjtcblxuZXhwb3J0IGZ1bmN0aW9uIExvZ28oKSB7XG4gIHJldHVybiA8SW5zb21WUE5Mb2dvIHZhcmlhbnQ9XCJmdWxsXCIgc2l6ZT1cImxnXCIgLz47XG59XG5cbi8vIEV4cG9ydCB0aGUgbWFpbiBsb2dvIGNvbXBvbmVudCBmb3Igb3RoZXIgdXNlc1xuZXhwb3J0IHsgSW5zb21WUE5Mb2dvIH0gZnJvbSBcIi4vbG9nby9JbnNvbVZQTkxvZ29cIjtcbiJdLCJuYW1lcyI6WyJJbnNvbVZQTkxvZ28iLCJMb2dvIiwidmFyaWFudCIsInNpemUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/logo.tsx\n"));

/***/ })

});