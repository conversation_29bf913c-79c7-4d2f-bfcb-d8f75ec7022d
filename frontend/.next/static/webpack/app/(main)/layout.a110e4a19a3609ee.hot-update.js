"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/Layouts/header/notification/index.tsx":
/*!**************************************************************!*\
  !*** ./src/components/Layouts/header/notification/index.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationNew: () => (/* binding */ NotificationNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_dropdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/dropdown */ \"(app-pages-browser)/./src/components/ui/dropdown.tsx\");\n/* harmony import */ var _hooks_use_mobile__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-mobile */ \"(app-pages-browser)/./src/hooks/use-mobile.ts\");\n/* harmony import */ var _hooks_useNotifications__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useNotifications */ \"(app-pages-browser)/./src/hooks/useNotifications.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons */ \"(app-pages-browser)/./src/components/Layouts/header/notification/icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ NotificationNew auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Notification type icons\nconst getNotificationIcon = (type)=>{\n    switch(type){\n        case 'trial':\n            return '🆓';\n        case 'monthly':\n            return '📅';\n        case 'hourly':\n            return '⏰';\n        case 'renewal':\n            return '🔄';\n        case 'topup':\n            return '💰';\n        case 'payment':\n            return '💳';\n        case 'billing':\n            return '📊';\n        default:\n            return '🔔';\n    }\n};\nfunction NotificationNew() {\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const isMobile = (0,_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_2__.useIsMobile)();\n    const { notifications, stats, loading, error, fetchNotifications, markAsRead, markAllAsRead, hasUnread, unreadCount } = (0,_hooks_useNotifications__WEBPACK_IMPORTED_MODULE_3__.useNotifications)();\n    // Fetch notifications when dropdown opens\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"NotificationNew.useEffect\": ()=>{\n            if (isOpen && (!notifications || notifications.length === 0)) {\n                fetchNotifications(1, 10);\n            }\n        }\n    }[\"NotificationNew.useEffect\"], [\n        isOpen,\n        notifications === null || notifications === void 0 ? void 0 : notifications.length,\n        fetchNotifications\n    ]);\n    const handleNotificationClick = async (notification)=>{\n        if (!notification.is_read) {\n            try {\n                await markAsRead([\n                    notification.id\n                ]);\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n        setIsOpen(false);\n    };\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await markAllAsRead();\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown__WEBPACK_IMPORTED_MODULE_1__.Dropdown, {\n        isOpen: isOpen,\n        setIsOpen: setIsOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown__WEBPACK_IMPORTED_MODULE_1__.DropdownTrigger, {\n                className: \"grid size-12 place-items-center rounded-full border bg-gray-2 text-dark outline-none hover:text-primary focus-visible:border-primary focus-visible:text-primary dark:border-dark-4 dark:bg-dark-3 dark:text-white dark:focus-visible:border-primary\",\n                \"aria-label\": \"View Notifications\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_6__.BellIcon, {}, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        hasUnread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"absolute -top-1 -right-1 z-1 flex size-5 items-center justify-center rounded-full bg-red text-xs text-white font-medium\",\n                            children: unreadCount > 9 ? '9+' : unreadCount\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown__WEBPACK_IMPORTED_MODULE_1__.DropdownContent, {\n                align: isMobile ? \"end\" : \"center\",\n                className: \"border border-stroke bg-white px-0 py-0 shadow-md dark:border-dark-3 dark:bg-gray-dark min-[350px]:min-w-[20rem] max-w-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between px-4 py-3 border-b border-stroke dark:border-dark-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-medium text-dark dark:text-white\",\n                                children: [\n                                    \"Notifikasi \",\n                                    unreadCount > 0 && \"(\".concat(unreadCount, \")\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleMarkAllAsRead,\n                                className: \"text-xs text-primary hover:underline\",\n                                children: \"Tandai semua dibaca\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-[23rem] overflow-y-auto\",\n                        children: loading && (!notifications || notifications.length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-body-color dark:text-dark-6\",\n                                children: \"Memuat notifikasi...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-red-500\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this) : !notifications || notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-body-color dark:text-dark-6\",\n                                children: \"Tidak ada notifikasi\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-0\",\n                            children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    role: \"menuitem\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNotificationClick(notification),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full flex items-start gap-3 px-4 py-3 text-left outline-none hover:bg-gray-2 focus-visible:bg-gray-2 dark:hover:bg-dark-3 dark:focus-visible:bg-dark-3 border-b border-stroke dark:border-dark-3 last:border-b-0\", !notification.is_read && \"bg-blue-50 dark:bg-blue-900/20\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 text-lg mt-0.5\",\n                                                children: getNotificationIcon(notification.type)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sm font-medium text-dark dark:text-white\", !notification.is_read && \"font-semibold\"),\n                                                                children: notification.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            !notification.is_read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0 size-2 rounded-full bg-primary ml-2 mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-body-color dark:text-dark-6 mt-1 line-clamp-2\",\n                                                        children: notification.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-body-color dark:text-dark-6 mt-1\",\n                                                        children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__.formatDistanceToNow)(new Date(notification.created_at), {\n                                                            addSuffix: true,\n                                                            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_8__.id\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 19\n                                    }, this)\n                                }, notification.id, false, {\n                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    notifications && notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-stroke dark:border-dark-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/notifications\",\n                            onClick: ()=>setIsOpen(false),\n                            className: \"block w-full py-3 text-center text-sm font-medium text-primary hover:bg-gray-2 dark:hover:bg-dark-3\",\n                            children: \"Lihat semua notifikasi\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/notification/index.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationNew, \"7rbMgAZTfbZXA5fBDmvhoeP07nA=\", false, function() {\n    return [\n        _hooks_use_mobile__WEBPACK_IMPORTED_MODULE_2__.useIsMobile,\n        _hooks_useNotifications__WEBPACK_IMPORTED_MODULE_3__.useNotifications\n    ];\n});\n_c = NotificationNew;\nvar _c;\n$RefreshReg$(_c, \"NotificationNew\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Layouts/header/notification/index.tsx\n"));

/***/ })

});