"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/Layouts/header/index.tsx":
/*!*************************************************!*\
  !*** ./src/components/Layouts/header/index.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../sidebar/sidebar-context */ \"(app-pages-browser)/./src/components/Layouts/sidebar/sidebar-context.tsx\");\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons */ \"(app-pages-browser)/./src/components/Layouts/header/icons.tsx\");\n/* harmony import */ var _notification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notification */ \"(app-pages-browser)/./src/components/Layouts/header/notification/index.tsx\");\n/* harmony import */ var _theme_toggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./theme-toggle */ \"(app-pages-browser)/./src/components/Layouts/header/theme-toggle/index.tsx\");\n/* harmony import */ var _user_info__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./user-info */ \"(app-pages-browser)/./src/components/Layouts/header/user-info/index.tsx\");\n/* harmony import */ var _UserBalance__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./UserBalance */ \"(app-pages-browser)/./src/components/Layouts/header/UserBalance.tsx\");\n/* harmony import */ var _components_logo_InsomVPNLogo__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/logo/InsomVPNLogo */ \"(app-pages-browser)/./src/components/logo/InsomVPNLogo.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const { toggleSidebar, isMobile } = (0,_sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_2__.useSidebarContext)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-30 flex items-center justify-between border-b border-stroke bg-white px-4 py-5 shadow-1 dark:border-stroke-dark dark:bg-gray-dark md:px-5 2xl:px-10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleSidebar,\n                className: \"rounded-lg border px-1.5 py-1 dark:border-stroke-dark dark:bg-[#020D1A] hover:dark:bg-[#FFFFFF1A] lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_3__.MenuIcon, {}, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: \"Toggle Sidebar\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                className: \"ml-2 max-[430px]:hidden min-[375px]:ml-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_logo_InsomVPNLogo__WEBPACK_IMPORTED_MODULE_8__.InsomVPNLogo, {\n                    variant: \"icon-only\",\n                    size: \"md\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-xl:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"mb-0.5 text-heading-5 font-bold text-dark dark:text-white\",\n                        children: \"Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium\",\n                        children: \"Admin Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 items-center justify-end gap-2 min-[375px]:gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserBalance__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_toggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggleSwitch, {}, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_notification__WEBPACK_IMPORTED_MODULE_4__.NotificationNew, {}, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_info__WEBPACK_IMPORTED_MODULE_6__.UserInfo, {}, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"+zH/FL/1wsB7bX/FR68I7PuPtqI=\", false, function() {\n    return [\n        _sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_2__.useSidebarContext\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Layouts/header/index.tsx\n"));

/***/ })

});