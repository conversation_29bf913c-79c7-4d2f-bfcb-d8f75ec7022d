"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/Layouts/sidebar/data/index.ts":
/*!******************************************************!*\
  !*** ./src/components/Layouts/sidebar/data/index.ts ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NAV_DATA: () => (/* binding */ NAV_DATA)\n/* harmony export */ });\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../icons */ \"(app-pages-browser)/./src/components/Layouts/sidebar/icons.tsx\");\n\nconst NAV_DATA = [\n    {\n        label: \"ADMIN\",\n        items: [\n            {\n                title: \"Admin\",\n                icon: _icons__WEBPACK_IMPORTED_MODULE_0__.UsersIcon,\n                adminOnly: true,\n                items: [\n                    {\n                        title: \"User Management\",\n                        url: \"/admin/users\"\n                    },\n                    {\n                        title: \"Server Management\",\n                        url: \"/admin/servers\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        label: \"LAYANAN VPN\",\n        items: [\n            {\n                title: \"Vmess\",\n                icon: _icons__WEBPACK_IMPORTED_MODULE_0__.FourCircle,\n                url: \"/product/vmess\",\n                items: [\n                    {\n                        title: \"Beli Layanan\",\n                        url: \"/product/vmess\"\n                    },\n                    {\n                        title: \"History Layanan\",\n                        url: \"/product/vmess/history\"\n                    }\n                ]\n            },\n            {\n                title: \"Vless\",\n                icon: _icons__WEBPACK_IMPORTED_MODULE_0__.FourCircle,\n                url: \"/product/vless\",\n                items: [\n                    {\n                        title: \"Beli Layanan\",\n                        url: \"/product/vless\"\n                    },\n                    {\n                        title: \"History Layanan\",\n                        url: \"/product/vless/history\"\n                    }\n                ]\n            },\n            {\n                title: \"Trojan\",\n                icon: _icons__WEBPACK_IMPORTED_MODULE_0__.FourCircle,\n                url: \"/product/trojan\",\n                items: [\n                    {\n                        title: \"Beli Layanan\",\n                        url: \"/product/trojan\"\n                    },\n                    {\n                        title: \"History Layanan\",\n                        url: \"/product/trojan/history\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        label: \"KEUANGAN\",\n        items: [\n            {\n                title: \"Top Up\",\n                url: \"/top-up\",\n                icon: _icons__WEBPACK_IMPORTED_MODULE_0__.CreditCard,\n                items: [\n                    {\n                        title: \"Top Up\",\n                        url: \"/top-up\"\n                    },\n                    {\n                        title: \"Riwayat\",\n                        url: \"/top-up/history\"\n                    }\n                ]\n            },\n            {\n                title: \"Transaksi\",\n                url: \"/transactions\",\n                icon: _icons__WEBPACK_IMPORTED_MODULE_0__.Table,\n                items: []\n            }\n        ]\n    },\n    {\n        label: \"AKUN\",\n        items: [\n            {\n                title: \"Notifikasi\",\n                url: \"/notifications\",\n                icon: _icons__WEBPACK_IMPORTED_MODULE_0__.BellIcon,\n                items: []\n            },\n            {\n                title: \"Profile\",\n                url: \"/profile\",\n                icon: _icons__WEBPACK_IMPORTED_MODULE_0__.User,\n                items: []\n            }\n        ]\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Layouts/sidebar/data/index.ts\n"));

/***/ })

});