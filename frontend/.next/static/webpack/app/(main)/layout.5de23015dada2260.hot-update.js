"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/logo/InsomVPNLogo.tsx":
/*!**********************************************!*\
  !*** ./src/components/logo/InsomVPNLogo.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InsomVPNLogo: () => (/* binding */ InsomVPNLogo),\n/* harmony export */   Logo: () => (/* binding */ Logo),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst InsomVPNLogo = (param)=>{\n    let { className = '', variant = 'full', size = 'md' } = param;\n    const sizeClasses = {\n        sm: 'h-6',\n        md: 'h-10',\n        lg: 'h-12',\n        xl: 'h-16'\n    };\n    const textSizes = {\n        sm: 'text-lg',\n        md: 'text-2xl',\n        lg: 'text-3xl',\n        xl: 'text-4xl'\n    };\n    // Logo Icon Component\n    const LogoIcon = (param)=>{\n        let { iconSize } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: iconSize,\n            viewBox: \"0 0 48 48\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M24 4L8 10V22C8 32 16 40.5 24 44C32 40.5 40 32 40 22V10L24 4Z\",\n                    fill: \"url(#shieldGradient)\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"1.5\",\n                    strokeLinejoin: \"round\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"16\",\n                    y: \"22\",\n                    width: \"16\",\n                    height: \"12\",\n                    rx: \"2\",\n                    fill: \"currentColor\",\n                    fillOpacity: \"0.9\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M20 22V18C20 15.7909 21.7909 14 24 14C26.2091 14 28 15.7909 28 18V22\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2.5\",\n                    strokeLinecap: \"round\",\n                    fill: \"none\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"24\",\n                    cy: \"27\",\n                    r: \"2\",\n                    fill: \"white\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"23\",\n                    y: \"27\",\n                    width: \"2\",\n                    height: \"4\",\n                    fill: \"white\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 16L16 18M32 18L36 16\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeOpacity: \"0.7\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"shieldGradient\",\n                        x1: \"0%\",\n                        y1: \"0%\",\n                        x2: \"100%\",\n                        y2: \"100%\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0%\",\n                                stopColor: \"#3B82F6\",\n                                stopOpacity: \"0.2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"50%\",\n                                stopColor: \"#1D4ED8\",\n                                stopOpacity: \"0.3\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"100%\",\n                                stopColor: \"#1E40AF\",\n                                stopOpacity: \"0.4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n            lineNumber: 30,\n            columnNumber: 5\n        }, undefined);\n    };\n    if (variant === 'icon-only') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoIcon, {\n                iconSize: sizeClasses[size]\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (variant === 'text-only') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-primary \".concat(textSizes[size]),\n                children: \"InsomVPN\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoIcon, {\n                iconSize: sizeClasses[size]\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-dark dark:text-white \".concat(textSizes[size]),\n                children: [\n                    \"Insom\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-primary\",\n                        children: \"VPN\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 14\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/logo/InsomVPNLogo.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, undefined);\n};\n_c = InsomVPNLogo;\n// Alias untuk backward compatibility\nconst Logo = InsomVPNLogo;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InsomVPNLogo);\nvar _c;\n$RefreshReg$(_c, \"InsomVPNLogo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/logo/InsomVPNLogo.tsx\n"));

/***/ })

});