"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/Layouts/header/index.tsx":
/*!*************************************************!*\
  !*** ./src/components/Layouts/header/index.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../sidebar/sidebar-context */ \"(app-pages-browser)/./src/components/Layouts/sidebar/sidebar-context.tsx\");\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons */ \"(app-pages-browser)/./src/components/Layouts/header/icons.tsx\");\n/* harmony import */ var _notification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notification */ \"(app-pages-browser)/./src/components/Layouts/header/notification/index.tsx\");\n/* harmony import */ var _theme_toggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./theme-toggle */ \"(app-pages-browser)/./src/components/Layouts/header/theme-toggle/index.tsx\");\n/* harmony import */ var _user_info__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./user-info */ \"(app-pages-browser)/./src/components/Layouts/header/user-info/index.tsx\");\n/* harmony import */ var _UserBalance__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./UserBalance */ \"(app-pages-browser)/./src/components/Layouts/header/UserBalance.tsx\");\n/* harmony import */ var _components_logo_InsomVPNLogo__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/logo/InsomVPNLogo */ \"(app-pages-browser)/./src/components/logo/InsomVPNLogo.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const { data: session } = useSession();\n    const { toggleSidebar, isMobile } = (0,_sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_2__.useSidebarContext)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-30 flex items-center justify-between border-b border-stroke bg-white px-4 py-5 shadow-1 dark:border-stroke-dark dark:bg-gray-dark md:px-5 2xl:px-10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleSidebar,\n                className: \"rounded-lg border px-1.5 py-1 dark:border-stroke-dark dark:bg-[#020D1A] hover:dark:bg-[#FFFFFF1A] lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_3__.MenuIcon, {}, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: \"Toggle Sidebar\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                className: \"ml-2 max-[430px]:hidden min-[375px]:ml-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_logo_InsomVPNLogo__WEBPACK_IMPORTED_MODULE_8__.InsomVPNLogo, {\n                    variant: \"icon-only\",\n                    size: \"md\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-xl:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"mb-0.5 text-heading-5 font-bold text-dark dark:text-white\",\n                        children: \"Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium\",\n                        children: \"Admin Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 items-center justify-end gap-2 min-[375px]:gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserBalance__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_toggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggleSwitch, {}, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_notification__WEBPACK_IMPORTED_MODULE_4__.NotificationNew, {}, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_info__WEBPACK_IMPORTED_MODULE_6__.UserInfo, {}, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/index.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"Q9jO/eE8pHEnD8bl8tYnnQXLcAo=\", true, function() {\n    return [\n        _sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_2__.useSidebarContext\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Layouts/header/index.tsx\n"));

/***/ })

});