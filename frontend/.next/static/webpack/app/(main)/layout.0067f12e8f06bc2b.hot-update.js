"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/Layouts/header/UserBalance.tsx":
/*!*******************************************************!*\
  !*** ./src/components/Layouts/header/UserBalance.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactUserBalance: () => (/* binding */ CompactUserBalance),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_BalanceContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/BalanceContext */ \"(app-pages-browser)/./src/contexts/BalanceContext.tsx\");\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons */ \"(app-pages-browser)/./src/components/Layouts/header/icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ CompactUserBalance,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst UserBalance = ()=>{\n    _s();\n    const { balance, isLoading } = (0,_contexts_BalanceContext__WEBPACK_IMPORTED_MODULE_1__.useBalance)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 rounded-full bg-gray-2 px-4 py-2 dark:bg-dark-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.WalletIcon, {\n                className: \"h-6 w-6 text-primary\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/UserBalance.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-dark dark:text-white\",\n                children: isLoading ? \"Memuat...\" : balance !== null ? new Intl.NumberFormat('id-ID', {\n                    style: 'currency',\n                    currency: 'IDR',\n                    minimumFractionDigits: 0,\n                    maximumFractionDigits: 0\n                }).format(balance) : \"Rp 0\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/UserBalance.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/UserBalance.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UserBalance, \"irXuPIQaG1Hr3khW9JIPHH5wUY0=\", false, function() {\n    return [\n        _contexts_BalanceContext__WEBPACK_IMPORTED_MODULE_1__.useBalance\n    ];\n});\n_c = UserBalance;\n// Compact version for mobile/dropdown\nconst CompactUserBalance = ()=>{\n    _s1();\n    const { balance, isLoading } = (0,_contexts_BalanceContext__WEBPACK_IMPORTED_MODULE_1__.useBalance)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-3 px-4 py-3 border-b border-stroke dark:border-dark-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.WalletIcon, {\n                className: \"h-5 w-5 text-primary\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/UserBalance.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-body-color dark:text-dark-6\",\n                        children: \"Saldo Anda\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/UserBalance.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-semibold text-dark dark:text-white\",\n                        children: isLoading ? \"Memuat...\" : balance !== null ? new Intl.NumberFormat('id-ID', {\n                            style: 'currency',\n                            currency: 'IDR',\n                            minimumFractionDigits: 0,\n                            maximumFractionDigits: 0\n                        }).format(balance) : \"Rp 0\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/UserBalance.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/UserBalance.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/header/UserBalance.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CompactUserBalance, \"irXuPIQaG1Hr3khW9JIPHH5wUY0=\", false, function() {\n    return [\n        _contexts_BalanceContext__WEBPACK_IMPORTED_MODULE_1__.useBalance\n    ];\n});\n_c1 = CompactUserBalance;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserBalance);\nvar _c, _c1;\n$RefreshReg$(_c, \"UserBalance\");\n$RefreshReg$(_c1, \"CompactUserBalance\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Layouts/header/UserBalance.tsx\n"));

/***/ })

});