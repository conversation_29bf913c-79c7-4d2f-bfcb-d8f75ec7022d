/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/purchase/route";
exports.ids = ["app/api/purchase/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpurchase%2Froute&page=%2Fapi%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpurchase%2Froute.ts&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpurchase%2Froute&page=%2Fapi%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpurchase%2Froute.ts&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_insomnia_project_vpn_shop_frondend_src_app_api_purchase_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/purchase/route.ts */ \"(rsc)/./src/app/api/purchase/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/purchase/route\",\n        pathname: \"/api/purchase\",\n        filename: \"route\",\n        bundlePath: \"app/api/purchase/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/project/vpn-shop/frondend/src/app/api/purchase/route.ts\",\n    nextConfigOutput,\n    userland: _home_insomnia_project_vpn_shop_frondend_src_app_api_purchase_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpurchase%2Froute&page=%2Fapi%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpurchase%2Froute.ts&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/purchase/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/purchase/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_apiClientEnhanced__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiClientEnhanced */ \"(rsc)/./src/lib/apiClientEnhanced.ts\");\n\n\n\n\nasync function POST(req) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Unauthorized'\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const body = await req.json();\n        const { kode_server, durasi, subscription_type, account_type, username, payment_method, tripay_method } = body;\n        // Validasi dasar untuk field yang harus selalu ada\n        if (!kode_server || !durasi || !subscription_type || !account_type) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Data tidak lengkap: kode_server, durasi, tipe langganan, dan tipe akun diperlukan.'\n            }, {\n                status: 400\n            });\n        }\n        // Validasi untuk username: hanya diperlukan untuk monthly dan hourly, tidak untuk trial\n        if (subscription_type !== 'trial' && (username === undefined || username === null)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Data tidak lengkap: username diperlukan untuk langganan bulanan dan per jam.'\n            }, {\n                status: 400\n            });\n        }\n        // Tentukan endpoint backend berdasarkan subscription_type\n        let endpoint = '';\n        let backendPayload = {};\n        if (subscription_type === 'trial') {\n            endpoint = '/purchase/trial';\n            backendPayload = {\n                kode_server,\n                protocol: account_type // trojan, vmess, vless\n            };\n        } else if (subscription_type === 'monthly') {\n            endpoint = '/purchase/monthly';\n            // Tentukan pembayaran dan metode berdasarkan pilihan user\n            const pembayaran = payment_method || 'SALDO';\n            const metode = pembayaran === 'TRIPAY' ? tripay_method || 'QRIS' : 'user_saldo';\n            backendPayload = {\n                kode_server,\n                bulan: durasi,\n                username,\n                protocol: account_type,\n                pembayaran,\n                metode\n            };\n        } else if (subscription_type === 'hourly') {\n            endpoint = '/purchase/hourly';\n            backendPayload = {\n                kode_server,\n                username,\n                protocol: account_type\n            };\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Tipe langganan tidak valid'\n            }, {\n                status: 400\n            });\n        }\n        console.log('Frontend received body:', JSON.stringify(body, null, 2));\n        console.log('Sending to backend endpoint:', endpoint);\n        console.log('Backend payload:', JSON.stringify(backendPayload, null, 2));\n        const apiClient = await (0,_lib_apiClientEnhanced__WEBPACK_IMPORTED_MODULE_3__.createApiClientFromRequest)(req);\n        const backendData = await apiClient.post(endpoint, backendPayload);\n        console.log('Backend Response Data:', JSON.stringify(backendData, null, 2));\n        // Tambahkan informasi untuk redirect ke response\n        const responseData = {\n            ...backendData,\n            redirect_info: {\n                subscription_type,\n                username: subscription_type === 'trial' ? backendData.username || backendData.data?.username : username,\n                kode_server,\n                should_redirect: true\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(responseData, {\n            status: 200\n        });\n    } catch (error) {\n        console.error('Error in purchase API route:', error);\n        // Jika error dari backend API, teruskan response asli\n        if (error.response && error.response.data) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(error.response.data, {\n                status: error.response.status || 500\n            });\n        }\n        // Jika error dari apiClient yang sudah diformat\n        if (error.message && error.message !== 'Network Error') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message,\n                details: error.details || null\n            }, {\n                status: 500\n            });\n        }\n        // Fallback untuk error lainnya\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Terjadi kesalahan internal pada server',\n            message: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/purchase/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/apiClientEnhanced.ts":
/*!**************************************!*\
  !*** ./src/lib/apiClientEnhanced.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   apiClientLegacy: () => (/* binding */ apiClientLegacy),\n/* harmony export */   createApiClientFromRequest: () => (/* binding */ createApiClientFromRequest),\n/* harmony export */   createAuthenticatedApiClient: () => (/* binding */ createAuthenticatedApiClient),\n/* harmony export */   safeApiCall: () => (/* binding */ safeApiCall)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/jwt */ \"(rsc)/./node_modules/next-auth/jwt/index.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_jwt__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * Enhanced API Client dengan fitur tambahan untuk mengurangi pengulangan kode\n * dan meningkatkan maintainability\n */ \n\n\n// ===== ENHANCED API CLIENT =====\nclass ApiClient {\n    constructor(baseUrl, timeout = 10000){\n        this.baseUrl = baseUrl || \"http://localhost:8000/api/v1\" || 0;\n        this.defaultTimeout = timeout;\n    }\n    // Helper method untuk mendapatkan URL lengkap untuk foto\n    getPhotoUrl(photoPath, bustCache = false) {\n        if (!photoPath) {\n            return \"/images/user/user-03.png\"; // Default fallback\n        }\n        // Jika sudah URL lengkap, return as is\n        if (photoPath.startsWith('http')) {\n            return photoPath;\n        }\n        // Extract base URL dari API URL (hapus /api/v1)\n        const apiUrl = \"http://localhost:8000/api/v1\" || 0;\n        const baseUrl = apiUrl.replace('/api/v1', '');\n        let url = `${baseUrl}/${photoPath}`;\n        // Tambahkan cache busting jika diperlukan\n        if (bustCache) {\n            url += `?t=${Date.now()}`;\n        }\n        return url;\n    }\n    async makeRequest(path, options = {}) {\n        const { token, skipAuth, timeout, ...restOptions } = options;\n        const headers = new Headers(restOptions.headers || {});\n        // Set Content-Type jika belum ada dan bukan FormData\n        if (!headers.has('Content-Type') && restOptions.method !== 'GET' && !(restOptions.body instanceof FormData)) {\n            headers.set('Content-Type', 'application/json');\n        }\n        // Tambahkan token otentikasi jika tersedia dan tidak di-skip\n        if (token && !skipAuth) {\n            headers.set('Authorization', `Bearer ${token}`);\n        }\n        // Setup timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), timeout || this.defaultTimeout);\n        try {\n            const response = await fetch(`${this.baseUrl}${path}`, {\n                ...restOptions,\n                headers,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            const data = await response.json();\n            if (!response.ok) {\n                const error = new Error(data.error || data.message || `HTTP ${response.status}: ${response.statusText}`);\n                error.status = response.status;\n                error.details = data.details || null;\n                error.response = {\n                    data,\n                    status: response.status\n                };\n                throw error;\n            }\n            return data;\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error instanceof Error && error.name === 'AbortError') {\n                throw new Error('Request timeout');\n            }\n            throw error;\n        }\n    }\n    // ===== HTTP METHODS =====\n    async get(path, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'GET'\n        });\n    }\n    async post(path, data, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'POST',\n            body: data instanceof FormData ? data : data ? JSON.stringify(data) : undefined\n        });\n    }\n    async put(path, data, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async delete(path, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'DELETE'\n        });\n    }\n    async patch(path, data, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'PATCH',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n}\n// ===== SINGLETON INSTANCE =====\nconst apiClient = new ApiClient();\n// ===== HELPER FUNCTIONS =====\n// Untuk Server Components\nasync function createAuthenticatedApiClient() {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session?.accessToken) {\n        throw new Error('Tidak terautentikasi atau token tidak ada.');\n    }\n    const client = new ApiClient();\n    const token = session.accessToken;\n    // Override methods dengan token yang sudah terikat\n    client.get = (path, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'GET',\n            token\n        });\n    client.post = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'POST',\n            body: data instanceof FormData ? data : data ? JSON.stringify(data) : undefined,\n            token\n        });\n    client.put = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined,\n            token\n        });\n    client.delete = (path, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'DELETE',\n            token\n        });\n    client.patch = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'PATCH',\n            body: data ? JSON.stringify(data) : undefined,\n            token\n        });\n    return client;\n}\n// Untuk API Routes\nasync function createApiClientFromRequest(req) {\n    const token = await (0,next_auth_jwt__WEBPACK_IMPORTED_MODULE_2__.getToken)({\n        req,\n        secret: process.env.NEXTAUTH_SECRET\n    });\n    if (!token?.accessToken) {\n        throw new Error('Tidak terautentikasi atau token tidak ada.');\n    }\n    const client = new ApiClient();\n    const accessToken = token.accessToken;\n    // Override methods dengan token yang sudah terikat\n    client.get = (path, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'GET',\n            token: accessToken\n        });\n    client.post = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'POST',\n            body: data instanceof FormData ? data : data ? JSON.stringify(data) : undefined,\n            token: accessToken\n        });\n    client.put = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined,\n            token: accessToken\n        });\n    client.delete = (path, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'DELETE',\n            token: accessToken\n        });\n    client.patch = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'PATCH',\n            body: data ? JSON.stringify(data) : undefined,\n            token: accessToken\n        });\n    return client;\n}\n// Error handling wrapper\nasync function safeApiCall(apiCall, fallback, onError) {\n    try {\n        return await apiCall();\n    } catch (error) {\n        const err = error instanceof Error ? error : new Error(String(error));\n        console.error('API call failed:', err.message);\n        if (onError) {\n            onError(err);\n        }\n        return fallback || null;\n    }\n}\n// ===== BACKWARD COMPATIBILITY =====\n// Untuk menjaga kompatibilitas dengan apiClient.ts yang sudah ada\nasync function apiClientLegacy(path, options = {}) {\n    return apiClient.makeRequest(path, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/apiClientEnhanced.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n\nconst authOptions = {\n    providers: [\n        // Provider untuk login email/password tradisional\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"credentials\",\n            name: \"Credentials\",\n            credentials: {\n                identifier: {\n                    label: \"Identifier\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials) {\n                    return null;\n                }\n                try {\n                    // Panggil endpoint login di backend Anda\n                    const res = await fetch(`${\"http://localhost:8000/api/v1\"}/auth/login`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            identifier: credentials.identifier,\n                            password: credentials.password\n                        })\n                    });\n                    const data = await res.json();\n                    // Jika login gagal, backend sekarang mengembalikan { user, accessToken }\n                    if (!res.ok || !data.user || !data.accessToken) {\n                        const errorMessage = data.error || \"Kredensial tidak valid atau terjadi kesalahan.\";\n                        throw new Error(errorMessage);\n                    }\n                    // Kembalikan objek yang akan disimpan di session JWT\n                    // Strukturnya sekarang sama dengan provider telegram\n                    return {\n                        ...data.user,\n                        accessToken: data.accessToken\n                    };\n                } catch (error) {\n                    // Log error untuk debugging di sisi server\n                    console.error(\"Authorize Error:\", error);\n                    // Lempar error agar bisa ditangkap oleh NextAuth dan ditampilkan ke pengguna\n                    throw new Error(error.message || \"Terjadi kesalahan saat mencoba masuk.\");\n                }\n            }\n        }),\n        // Provider untuk login via Telegram\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"telegram\",\n            name: \"Telegram\",\n            credentials: {\n                user_data: {\n                    label: \"User Data\",\n                    type: \"text\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.user_data) {\n                    return null;\n                }\n                try {\n                    const telegramUserData = JSON.parse(credentials.user_data);\n                    const res = await fetch(`${\"http://localhost:8000/api/v1\"}/auth/telegram`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(telegramUserData)\n                    });\n                    const responseData = await res.json();\n                    if (res.ok && responseData && responseData.user && responseData.accessToken) {\n                        // Backend mengembalikan { user: {...}, accessToken: \"...\" }\n                        // Kita harus mengembalikan objek yang sama agar NextAuth dapat memprosesnya di callback jwt\n                        return responseData;\n                    }\n                    // Jika backend mengembalikan error atau formatnya salah, log error tersebut\n                    console.error(\"Telegram auth failed:\", responseData.error || \"Unknown error from backend\");\n                    return null;\n                } catch (error) {\n                    console.error(\"Error in Telegram authorize function:\", error);\n                    // Melempar error agar bisa ditangkap oleh frontend jika diperlukan\n                    throw new Error(error.message || \"An unexpected error occurred during Telegram login.\");\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            // Saat login pertama kali, objek 'user' dari provider tersedia.\n            // Kita salin accessToken dan data user ke dalam token JWT.\n            if (user) {\n                return {\n                    ...token,\n                    accessToken: user.accessToken,\n                    user: user\n                };\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Setiap kali sesi diakses, data dari token disalin ke objek sesi.\n            // Ini membuat data tersedia di sisi client melalui useSession() atau getServerSession().\n            session.user = token.user;\n            session.accessToken = token.accessToken;\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/'\n    },\n    session: {\n        strategy: \"jwt\",\n        // Session expires in 30 minutes, matching the backend token lifetime.\n        maxAge: parseInt(process.env.NEXTAUTH_SESSION_MAX_AGE || '1800', 10)\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/@babel","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpurchase%2Froute&page=%2Fapi%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpurchase%2Froute.ts&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();