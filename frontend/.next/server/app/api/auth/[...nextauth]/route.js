/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_insomnia_project_vpn_shop_frondend_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/project/vpn-shop/frondend/src/app/api/auth/[...nextauth]/route.ts\",\n    nextConfigOutput,\n    userland: _home_insomnia_project_vpn_shop_frondend_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFDUTtBQUV6QyxNQUFNRSxVQUFVRixnREFBUUEsQ0FBQ0Msa0RBQVdBO0FBRU8iLCJzb3VyY2VzIjpbIi9ob21lL2luc29tbmlhL3Byb2plY3QvdnBuLXNob3AvZnJvbmRlbmQvc3JjL2FwcC9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdL3JvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOZXh0QXV0aCBmcm9tIFwibmV4dC1hdXRoXCI7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gXCJAL2xpYi9hdXRoXCI7XG5cbmNvbnN0IGhhbmRsZXIgPSBOZXh0QXV0aChhdXRoT3B0aW9ucyk7XG5cbmV4cG9ydCB7IGhhbmRsZXIgYXMgR0VULCBoYW5kbGVyIGFzIFBPU1QgfTtcbiJdLCJuYW1lcyI6WyJOZXh0QXV0aCIsImF1dGhPcHRpb25zIiwiaGFuZGxlciIsIkdFVCIsIlBPU1QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n\nconst authOptions = {\n    providers: [\n        // Provider untuk login email/password tradisional\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"credentials\",\n            name: \"Credentials\",\n            credentials: {\n                identifier: {\n                    label: \"Identifier\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials) {\n                    return null;\n                }\n                try {\n                    // Panggil endpoint login di backend Anda\n                    const res = await fetch(`${\"http://localhost:8000/api/v1\"}/auth/login`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            identifier: credentials.identifier,\n                            password: credentials.password\n                        })\n                    });\n                    const data = await res.json();\n                    // Jika login gagal, backend sekarang mengembalikan { user, accessToken }\n                    if (!res.ok || !data.user || !data.accessToken) {\n                        const errorMessage = data.error || \"Kredensial tidak valid atau terjadi kesalahan.\";\n                        throw new Error(errorMessage);\n                    }\n                    // Kembalikan objek yang akan disimpan di session JWT\n                    // Strukturnya sekarang sama dengan provider telegram\n                    return {\n                        ...data.user,\n                        accessToken: data.accessToken\n                    };\n                } catch (error) {\n                    // Log error untuk debugging di sisi server\n                    console.error(\"Authorize Error:\", error);\n                    // Lempar error agar bisa ditangkap oleh NextAuth dan ditampilkan ke pengguna\n                    throw new Error(error.message || \"Terjadi kesalahan saat mencoba masuk.\");\n                }\n            }\n        }),\n        // Provider untuk login via Telegram\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"telegram\",\n            name: \"Telegram\",\n            credentials: {\n                user_data: {\n                    label: \"User Data\",\n                    type: \"text\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.user_data) {\n                    return null;\n                }\n                try {\n                    const telegramUserData = JSON.parse(credentials.user_data);\n                    const res = await fetch(`${\"http://localhost:8000/api/v1\"}/auth/telegram`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(telegramUserData)\n                    });\n                    const responseData = await res.json();\n                    if (res.ok && responseData && responseData.user && responseData.accessToken) {\n                        // Backend mengembalikan { user: {...}, accessToken: \"...\" }\n                        // Kita harus mengembalikan objek yang sama agar NextAuth dapat memprosesnya di callback jwt\n                        return responseData;\n                    }\n                    // Jika backend mengembalikan error atau formatnya salah, log error tersebut\n                    console.error(\"Telegram auth failed:\", responseData.error || \"Unknown error from backend\");\n                    return null;\n                } catch (error) {\n                    console.error(\"Error in Telegram authorize function:\", error);\n                    // Melempar error agar bisa ditangkap oleh frontend jika diperlukan\n                    throw new Error(error.message || \"An unexpected error occurred during Telegram login.\");\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            // Saat login pertama kali, objek 'user' dari provider tersedia.\n            // Kita salin accessToken dan data user ke dalam token JWT.\n            if (user) {\n                return {\n                    ...token,\n                    accessToken: user.accessToken,\n                    user: user\n                };\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Setiap kali sesi diakses, data dari token disalin ke objek sesi.\n            // Ini membuat data tersedia di sisi client melalui useSession() atau getServerSession().\n            session.user = token.user;\n            session.accessToken = token.accessToken;\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/'\n    },\n    session: {\n        strategy: \"jwt\",\n        // Session expires in 30 minutes, matching the backend token lifetime.\n        maxAge: parseInt(process.env.NEXTAUTH_SESSION_MAX_AGE || '1800', 10)\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();