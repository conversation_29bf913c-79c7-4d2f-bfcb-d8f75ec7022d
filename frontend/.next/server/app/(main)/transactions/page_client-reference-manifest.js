globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(main)/transactions/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(ssr)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(main)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/Announcements.tsx":{"*":{"id":"(ssr)/./src/components/Dashboard/Announcements.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/ServerSlideshow.tsx":{"*":{"id":"(ssr)/./src/components/Dashboard/ServerSlideshow.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/TopServers.tsx":{"*":{"id":"(ssr)/./src/components/Dashboard/TopServers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/Transactions.tsx":{"*":{"id":"(ssr)/./src/components/Dashboard/Transactions.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/product/vmess/ServerListClient.tsx":{"*":{"id":"(ssr)/./src/app/(main)/product/vmess/ServerListClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/product/vmess/[id]/OrderDetailClient.tsx":{"*":{"id":"(ssr)/./src/app/(main)/product/vmess/[id]/OrderDetailClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/detail/[username]/[server_code]/error.tsx":{"*":{"id":"(ssr)/./src/app/(main)/detail/[username]/[server_code]/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/detail/[username]/[server_code]/AccountDetailClient.tsx":{"*":{"id":"(ssr)/./src/app/(main)/detail/[username]/[server_code]/AccountDetailClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Tables/TransactionTable.tsx":{"*":{"id":"(ssr)/./src/components/Tables/TransactionTable.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/notifications/page.tsx":{"*":{"id":"(ssr)/./src/app/(main)/notifications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(auth)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Auth/SigninWithPassword.tsx":{"*":{"id":"(ssr)/./src/components/Auth/SigninWithPassword.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Auth/TelegramLoginWidget.tsx":{"*":{"id":"(ssr)/./src/components/Auth/TelegramLoginWidget.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Auth/Signup/index.tsx":{"*":{"id":"(ssr)/./src/components/Auth/Signup/index.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx":{"id":"(app-pages-browser)/./src/app/layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/client/app-dir/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/(main)/transactions/page","static/chunks/app/(main)/transactions/page.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/esm/client/app-dir/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/(main)/transactions/page","static/chunks/app/(main)/transactions/page.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/client/image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/(auth)/page","static/chunks/app/(auth)/page.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/esm/client/image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/(auth)/page","static/chunks/app/(auth)/page.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/app/(main)/layout.tsx":{"id":"(app-pages-browser)/./src/app/(main)/layout.tsx","name":"*","chunks":["app/(main)/layout","static/chunks/app/(main)/layout.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/components/Dashboard/Announcements.tsx":{"id":"(app-pages-browser)/./src/components/Dashboard/Announcements.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/components/Dashboard/ServerSlideshow.tsx":{"id":"(app-pages-browser)/./src/components/Dashboard/ServerSlideshow.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/components/Dashboard/TopServers.tsx":{"id":"(app-pages-browser)/./src/components/Dashboard/TopServers.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/components/Dashboard/Transactions.tsx":{"id":"(app-pages-browser)/./src/components/Dashboard/Transactions.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/lib/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/app/(main)/product/vmess/ServerListClient.tsx":{"id":"(app-pages-browser)/./src/app/(main)/product/vmess/ServerListClient.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/app/(main)/product/vmess/[id]/OrderDetailClient.tsx":{"id":"(app-pages-browser)/./src/app/(main)/product/vmess/[id]/OrderDetailClient.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/app/(main)/detail/[username]/[server_code]/error.tsx":{"id":"(app-pages-browser)/./src/app/(main)/detail/[username]/[server_code]/error.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/app/(main)/detail/[username]/[server_code]/AccountDetailClient.tsx":{"id":"(app-pages-browser)/./src/app/(main)/detail/[username]/[server_code]/AccountDetailClient.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/components/Tables/TransactionTable.tsx":{"id":"(app-pages-browser)/./src/components/Tables/TransactionTable.tsx","name":"*","chunks":["app/(main)/transactions/page","static/chunks/app/(main)/transactions/page.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/app/(main)/notifications/page.tsx":{"id":"(app-pages-browser)/./src/app/(main)/notifications/page.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/app/(auth)/layout.tsx":{"id":"(app-pages-browser)/./src/app/(auth)/layout.tsx","name":"*","chunks":["app/(auth)/layout","static/chunks/app/(auth)/layout.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/components/Auth/SigninWithPassword.tsx":{"id":"(app-pages-browser)/./src/components/Auth/SigninWithPassword.tsx","name":"*","chunks":["app/(auth)/page","static/chunks/app/(auth)/page.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/components/Auth/TelegramLoginWidget.tsx":{"id":"(app-pages-browser)/./src/components/Auth/TelegramLoginWidget.tsx","name":"*","chunks":["app/(auth)/page","static/chunks/app/(auth)/page.js"],"async":false},"/home/<USER>/project/vpn-shop/frondend/src/components/Auth/Signup/index.tsx":{"id":"(app-pages-browser)/./src/components/Auth/Signup/index.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/home/<USER>/project/vpn-shop/frondend/src/":[],"/home/<USER>/project/vpn-shop/frondend/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/home/<USER>/project/vpn-shop/frondend/src/app/not-found":[],"/home/<USER>/project/vpn-shop/frondend/src/app/(main)/layout":[],"/home/<USER>/project/vpn-shop/frondend/src/app/(auth)/layout":[],"/home/<USER>/project/vpn-shop/frondend/src/app/(auth)/page":[],"/home/<USER>/project/vpn-shop/frondend/src/app/(main)/transactions/page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(rsc)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/layout.tsx":{"*":{"id":"(rsc)/./src/app/(main)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/Announcements.tsx":{"*":{"id":"(rsc)/./src/components/Dashboard/Announcements.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/ServerSlideshow.tsx":{"*":{"id":"(rsc)/./src/components/Dashboard/ServerSlideshow.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/TopServers.tsx":{"*":{"id":"(rsc)/./src/components/Dashboard/TopServers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/Transactions.tsx":{"*":{"id":"(rsc)/./src/components/Dashboard/Transactions.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/product/vmess/ServerListClient.tsx":{"*":{"id":"(rsc)/./src/app/(main)/product/vmess/ServerListClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/product/vmess/[id]/OrderDetailClient.tsx":{"*":{"id":"(rsc)/./src/app/(main)/product/vmess/[id]/OrderDetailClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/detail/[username]/[server_code]/error.tsx":{"*":{"id":"(rsc)/./src/app/(main)/detail/[username]/[server_code]/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/detail/[username]/[server_code]/AccountDetailClient.tsx":{"*":{"id":"(rsc)/./src/app/(main)/detail/[username]/[server_code]/AccountDetailClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Tables/TransactionTable.tsx":{"*":{"id":"(rsc)/./src/components/Tables/TransactionTable.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(main)/notifications/page.tsx":{"*":{"id":"(rsc)/./src/app/(main)/notifications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/layout.tsx":{"*":{"id":"(rsc)/./src/app/(auth)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Auth/SigninWithPassword.tsx":{"*":{"id":"(rsc)/./src/components/Auth/SigninWithPassword.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Auth/TelegramLoginWidget.tsx":{"*":{"id":"(rsc)/./src/components/Auth/TelegramLoginWidget.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Auth/Signup/index.tsx":{"*":{"id":"(rsc)/./src/components/Auth/Signup/index.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}