/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboard)/notifications/page";
exports.ids = ["app/(dashboard)/notifications/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fnotifications%2Fpage&page=%2F(dashboard)%2Fnotifications%2Fpage&appPaths=%2F(dashboard)%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fnotifications%2Fpage.tsx&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fnotifications%2Fpage&page=%2F(dashboard)%2Fnotifications%2Fpage&appPaths=%2F(dashboard)%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fnotifications%2Fpage.tsx&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/notifications/page.tsx */ \"(rsc)/./src/app/(dashboard)/notifications/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: [\n        'notifications',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx\"],\n'not-found': [module1, \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboard)/notifications/page\",\n        pathname: \"/notifications\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fnotifications%2Fpage&page=%2F(dashboard)%2Fnotifications%2Fpage&appPaths=%2F(dashboard)%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fnotifications%2Fpage.tsx&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZpbnNvbW5pYSUyRnByb2plY3QlMkZ2cG4tc2hvcCUyRmZyb25kZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZhcHAtZGlyJTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZpbnNvbW5pYSUyRnByb2plY3QlMkZ2cG4tc2hvcCUyRmZyb25kZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZpbWFnZS1jb21wb25lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFxSztBQUNySztBQUNBLHNOQUE4SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIi9ob21lL2luc29tbmlhL3Byb2plY3QvdnBuLXNob3AvZnJvbmRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvYXBwLWRpci9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2ltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZpbnNvbW5pYSUyRnByb2plY3QlMkZ2cG4tc2hvcCUyRmZyb25kZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZhcHAtZGlyJTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZpbnNvbW5pYSUyRnByb2plY3QlMkZ2cG4tc2hvcCUyRmZyb25kZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZpbWFnZS1jb21wb25lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFxSztBQUNySztBQUNBLHNOQUE4SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIi9ob21lL2luc29tbmlhL3Byb2plY3QvdnBuLXNob3AvZnJvbmRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvYXBwLWRpci9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2ltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2F(dashboard)%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2F(dashboard)%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/notifications/page.tsx */ \"(rsc)/./src/app/(dashboard)/notifications/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZpbnNvbW5pYSUyRnByb2plY3QlMkZ2cG4tc2hvcCUyRmZyb25kZW5kJTJGc3JjJTJGYXBwJTJGKGRhc2hib2FyZCklMkZub3RpZmljYXRpb25zJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUF3SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvaW5zb21uaWEvcHJvamVjdC92cG4tc2hvcC9mcm9uZGVuZC9zcmMvYXBwLyhkYXNoYm9hcmQpL25vdGlmaWNhdGlvbnMvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2F(dashboard)%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2F(dashboard)%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2F(dashboard)%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/notifications/page.tsx */ \"(ssr)/./src/app/(dashboard)/notifications/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZpbnNvbW5pYSUyRnByb2plY3QlMkZ2cG4tc2hvcCUyRmZyb25kZW5kJTJGc3JjJTJGYXBwJTJGKGRhc2hib2FyZCklMkZub3RpZmljYXRpb25zJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUF3SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvaW5zb21uaWEvcHJvamVjdC92cG4tc2hvcC9mcm9uZGVuZC9zcmMvYXBwLyhkYXNoYm9hcmQpL25vdGlmaWNhdGlvbnMvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2F(dashboard)%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZpbnNvbW5pYSUyRnByb2plY3QlMkZ2cG4tc2hvcCUyRmZyb25kZW5kJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQWdHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL3NyYy9hcHAvbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZpbnNvbW5pYSUyRnByb2plY3QlMkZ2cG4tc2hvcCUyRmZyb25kZW5kJTJGc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQWdHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL3NyYy9hcHAvbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(dashboard)/notifications/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/(dashboard)/notifications/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useNotifications__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useNotifications */ \"(ssr)/./src/hooks/useNotifications.ts\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(ssr)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Notification type icons\nconst getNotificationIcon = (type)=>{\n    switch(type){\n        case 'trial':\n            return '🆓';\n        case 'monthly':\n            return '📅';\n        case 'hourly':\n            return '⏰';\n        case 'renewal':\n            return '🔄';\n        case 'topup':\n            return '💰';\n        case 'payment':\n            return '💳';\n        case 'billing':\n            return '📊';\n        default:\n            return '🔔';\n    }\n};\nconst getNotificationTypeLabel = (type)=>{\n    switch(type){\n        case 'trial':\n            return 'Trial';\n        case 'monthly':\n            return 'Pembelian Bulanan';\n        case 'hourly':\n            return 'Pembelian Hourly';\n        case 'renewal':\n            return 'Perpanjangan';\n        case 'topup':\n            return 'Top Up';\n        case 'payment':\n            return 'Pembayaran';\n        case 'billing':\n            return 'Billing';\n        default:\n            return 'Notifikasi';\n    }\n};\nfunction NotificationsPage() {\n    const { notifications, stats, loading, error, fetchNotifications, markAsRead, markAllAsRead, deleteNotification, hasUnread, unreadCount } = (0,_hooks_useNotifications__WEBPACK_IMPORTED_MODULE_2__.useNotifications)();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [typeFilter, setTypeFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initial fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationsPage.useEffect\": ()=>{\n            fetchNotifications(1, 20);\n        }\n    }[\"NotificationsPage.useEffect\"], [\n        fetchNotifications\n    ]);\n    // Load more notifications\n    const loadMore = async ()=>{\n        if (loadingMore) return;\n        setLoadingMore(true);\n        try {\n            await fetchNotifications(currentPage + 1, 20);\n            setCurrentPage((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Failed to load more notifications:', error);\n        } finally{\n            setLoadingMore(false);\n        }\n    };\n    // Filter notifications\n    const filteredNotifications = notifications?.filter((notification)=>{\n        if (filter === 'unread' && notification.is_read) return false;\n        if (filter === 'read' && !notification.is_read) return false;\n        if (typeFilter !== 'all' && notification.type !== typeFilter) return false;\n        return true;\n    }) || [];\n    // Handle notification click\n    const handleNotificationClick = async (notification)=>{\n        if (!notification.is_read) {\n            try {\n                await markAsRead([\n                    notification.id\n                ]);\n            } catch (error) {\n                console.error('Failed to mark notification as read:', error);\n            }\n        }\n    };\n    // Handle mark all as read\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await markAllAsRead();\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    // Handle delete notification\n    const handleDeleteNotification = async (notificationId, e)=>{\n        e.stopPropagation();\n        try {\n            await deleteNotification(notificationId);\n        } catch (error) {\n            console.error('Failed to delete notification:', error);\n        }\n    };\n    // Get unique notification types for filter\n    const notificationTypes = Array.from(new Set(notifications?.map((n)=>n.type) || []));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto max-w-4xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-dark dark:text-white\",\n                                    children: \"Notifikasi\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-body-color dark:text-dark-6 mt-1\",\n                                    children: [\n                                        stats.total_count,\n                                        \" total notifikasi\",\n                                        unreadCount > 0 && `, ${unreadCount} belum dibaca`\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        hasUnread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleMarkAllAsRead,\n                            className: \"rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90\",\n                            children: \"Tandai Semua Dibaca\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-wrap gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            {\n                                value: 'all',\n                                label: 'Semua'\n                            },\n                            {\n                                value: 'unread',\n                                label: 'Belum Dibaca'\n                            },\n                            {\n                                value: 'read',\n                                label: 'Sudah Dibaca'\n                            }\n                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setFilter(option.value),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"rounded-lg px-3 py-1.5 text-sm font-medium transition-colors\", filter === option.value ? \"bg-primary text-white\" : \"bg-gray-2 text-dark hover:bg-gray-3 dark:bg-dark-3 dark:text-white dark:hover:bg-dark-2\"),\n                                children: option.label\n                            }, option.value, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setTypeFilter('all'),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"rounded-lg px-3 py-1.5 text-sm font-medium transition-colors\", typeFilter === 'all' ? \"bg-primary text-white\" : \"bg-gray-2 text-dark hover:bg-gray-3 dark:bg-dark-3 dark:text-white dark:hover:bg-dark-2\"),\n                                children: \"Semua Tipe\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            notificationTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setTypeFilter(type),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"rounded-lg px-3 py-1.5 text-sm font-medium transition-colors\", typeFilter === type ? \"bg-primary text-white\" : \"bg-gray-2 text-dark hover:bg-gray-3 dark:bg-dark-3 dark:text-white dark:hover:bg-dark-2\"),\n                                    children: [\n                                        getNotificationIcon(type),\n                                        \" \",\n                                        getNotificationTypeLabel(type)\n                                    ]\n                                }, type, true, {\n                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: loading && (!notifications || notifications.length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-body-color dark:text-dark-6\",\n                        children: \"Memuat notifikasi...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, this) : filteredNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-4\",\n                                children: \"\\uD83D\\uDD14\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-body-color dark:text-dark-6\",\n                                children: filter === 'all' ? 'Tidak ada notifikasi' : filter === 'unread' ? 'Tidak ada notifikasi yang belum dibaca' : 'Tidak ada notifikasi yang sudah dibaca'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        filteredNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleNotificationClick(notification),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group relative cursor-pointer rounded-lg border p-4 transition-all hover:shadow-md\", notification.is_read ? \"border-stroke bg-white dark:border-dark-3 dark:bg-gray-dark\" : \"border-primary/20 bg-blue-50 dark:border-primary/30 dark:bg-blue-900/20\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 text-2xl\",\n                                            children: getNotificationIcon(notification.type)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-medium text-dark dark:text-white\", !notification.is_read && \"font-semibold\"),\n                                                                        children: notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"rounded-full bg-gray-2 px-2 py-0.5 text-xs text-body-color dark:bg-dark-3 dark:text-dark-6\",\n                                                                        children: getNotificationTypeLabel(notification.type)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    !notification.is_read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"size-2 rounded-full bg-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-body-color dark:text-dark-6 mb-2\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-body-color dark:text-dark-6\",\n                                                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(new Date(notification.created_at), {\n                                                                    addSuffix: true,\n                                                                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_5__.id\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleDeleteNotification(notification.id, e),\n                                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-700 p-1\",\n                                                        title: \"Hapus notifikasi\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"size-4\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 17\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this)),\n                        notifications && notifications.length >= 20 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadMore,\n                                disabled: loadingMore,\n                                className: \"rounded-lg border border-stroke bg-white px-6 py-2 text-sm font-medium text-dark hover:bg-gray-2 disabled:opacity-50 dark:border-dark-3 dark:bg-gray-dark dark:text-white dark:hover:bg-dark-2\",\n                                children: loadingMore ? 'Memuat...' : 'Muat Lebih Banyak'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(dashboard)/notifications/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _css_satoshi_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/css/satoshi.css */ \"(ssr)/./src/css/satoshi.css\");\n/* harmony import */ var _css_badge_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/css/badge.css */ \"(ssr)/./src/css/badge.css\");\n/* harmony import */ var _css_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/css/style.css */ \"(ssr)/./src/css/style.css\");\n/* harmony import */ var _css_form_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/css/form.css */ \"(ssr)/./src/css/form.css\");\n/* harmony import */ var _css_notification_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/css/notification.css */ \"(ssr)/./src/css/notification.css\");\n/* harmony import */ var _css_card_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/css/card.css */ \"(ssr)/./src/css/card.css\");\n/* harmony import */ var _css_tabs_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/css/tabs.css */ \"(ssr)/./src/css/tabs.css\");\n/* harmony import */ var flatpickr_dist_flatpickr_min_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! flatpickr/dist/flatpickr.min.css */ \"(ssr)/./node_modules/flatpickr/dist/flatpickr.min.css\");\n/* harmony import */ var jsvectormap_dist_jsvectormap_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jsvectormap/dist/jsvectormap.css */ \"(ssr)/./node_modules/jsvectormap/dist/jsvectormap.css\");\n/* harmony import */ var nextjs_toploader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! nextjs-toploader */ \"(ssr)/./node_modules/nextjs-toploader/dist/index.js\");\n/* harmony import */ var nextjs_toploader__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(nextjs_toploader__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./providers */ \"(ssr)/./src/app/providers.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"(ssr)/./src/contexts/NotificationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_11__.SessionProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_12__.Providers, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_13__.NotificationProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((nextjs_toploader__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                color: \"#5750F1\",\n                                showSpinner: false\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 15\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layouts_sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layouts/sidebar/sidebar-context */ \"(ssr)/./src/components/Layouts/sidebar/sidebar-context.tsx\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        defaultTheme: \"light\",\n        attribute: \"class\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layouts_sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_1__.SidebarProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/providers.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/providers.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRStFO0FBQ25DO0FBRXJDLFNBQVNFLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxxQkFDRSw4REFBQ0Ysc0RBQWFBO1FBQUNHLGNBQWE7UUFBUUMsV0FBVTtrQkFDNUMsNEVBQUNMLHdGQUFlQTtzQkFBRUc7Ozs7Ozs7Ozs7O0FBR3hCIiwic291cmNlcyI6WyIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL3NyYy9hcHAvcHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgU2lkZWJhclByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9MYXlvdXRzL3NpZGViYXIvc2lkZWJhci1jb250ZXh0XCI7XG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGRlZmF1bHRUaGVtZT1cImxpZ2h0XCIgYXR0cmlidXRlPVwiY2xhc3NcIj5cbiAgICAgIDxTaWRlYmFyUHJvdmlkZXI+e2NoaWxkcmVufTwvU2lkZWJhclByb3ZpZGVyPlxuICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTaWRlYmFyUHJvdmlkZXIiLCJUaGVtZVByb3ZpZGVyIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJkZWZhdWx0VGhlbWUiLCJhdHRyaWJ1dGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layouts/sidebar/sidebar-context.tsx":
/*!************************************************************!*\
  !*** ./src/components/Layouts/sidebar/sidebar-context.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),\n/* harmony export */   useSidebarContext: () => (/* binding */ useSidebarContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_mobile__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-mobile */ \"(ssr)/./src/hooks/use-mobile.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useSidebarContext,SidebarProvider auto */ \n\n\nconst SidebarContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(null);\nfunction useSidebarContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(SidebarContext);\n    if (!context) {\n        throw new Error(\"useSidebarContext must be used within a SidebarProvider\");\n    }\n    return context;\n}\nfunction SidebarProvider({ children, defaultOpen = true }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(defaultOpen);\n    const isMobile = (0,_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_1__.useIsMobile)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SidebarProvider.useEffect\": ()=>{\n            if (isMobile) {\n                setIsOpen(false);\n            } else {\n                setIsOpen(true);\n            }\n        }\n    }[\"SidebarProvider.useEffect\"], [\n        isMobile\n    ]);\n    function toggleSidebar() {\n        setIsOpen((prev)=>!prev);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContext.Provider, {\n        value: {\n            state: isOpen ? \"expanded\" : \"collapsed\",\n            isOpen,\n            setIsOpen,\n            isMobile,\n            toggleSidebar\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/components/Layouts/sidebar/sidebar-context.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layouts/sidebar/sidebar-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/NotificationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/NotificationContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   useNotification: () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useNotification,NotificationProvider auto */ \n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useNotification = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (!context) {\n        throw new Error('useNotification must be used within a NotificationProvider');\n    }\n    return context;\n};\nconst NotificationProvider = ({ children })=>{\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addNotification = (message, type)=>{\n        const id = new Date().getTime();\n        setNotifications((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        // Automatically remove the notification after 5 seconds\n        setTimeout(()=>{\n            setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n        }, 5000);\n    };\n    const removeNotification = (id)=>{\n        setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: {\n            addNotification\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-5 right-5 z-50 flex flex-col gap-3\",\n                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `notification notification-${notification.type}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"notification-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"notification-icon\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"notification-title\",\n                                                children: notification.type.charAt(0).toUpperCase() + notification.type.slice(1)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"notification-message\",\n                                                children: notification.message\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>removeNotification(notification.id),\n                                className: \"notification-close\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, notification.id, true, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/contexts/NotificationContext.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/NotificationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-mobile.ts":
/*!*********************************!*\
  !*** ./src/hooks/use-mobile.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MOBILE_BREAKPOINT: () => (/* binding */ MOBILE_BREAKPOINT),\n/* harmony export */   useIsMobile: () => (/* binding */ useIsMobile)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MOBILE_BREAKPOINT = 850;\nfunction useIsMobile() {\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIsMobile.useEffect\": ()=>{\n            const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\n            const onChange = {\n                \"useIsMobile.useEffect.onChange\": ()=>{\n                    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n                }\n            }[\"useIsMobile.useEffect.onChange\"];\n            setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n            mql.addEventListener(\"change\", onChange);\n            return ({\n                \"useIsMobile.useEffect\": ()=>mql.removeEventListener(\"change\", onChange)\n            })[\"useIsMobile.useEffect\"];\n        }\n    }[\"useIsMobile.useEffect\"], []);\n    return !!isMobile;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlLW1vYmlsZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTRDO0FBRXJDLE1BQU1FLG9CQUFvQixJQUFJO0FBRTlCLFNBQVNDO0lBQ2QsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdKLCtDQUFRQTtJQUV4Q0QsZ0RBQVNBO2lDQUFDO1lBQ1IsTUFBTU0sTUFBTUMsT0FBT0MsVUFBVSxDQUFDLENBQUMsWUFBWSxFQUFFTixvQkFBb0IsRUFBRSxHQUFHLENBQUM7WUFFdkUsTUFBTU87a0RBQVc7b0JBQ2ZKLFlBQVlFLE9BQU9HLFVBQVUsR0FBR1I7Z0JBQ2xDOztZQUVBRyxZQUFZRSxPQUFPRyxVQUFVLEdBQUdSO1lBRWhDSSxJQUFJSyxnQkFBZ0IsQ0FBQyxVQUFVRjtZQUMvQjt5Q0FBTyxJQUFNSCxJQUFJTSxtQkFBbUIsQ0FBQyxVQUFVSDs7UUFDakQ7Z0NBQUcsRUFBRTtJQUVMLE9BQU8sQ0FBQyxDQUFDTDtBQUNYIiwic291cmNlcyI6WyIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL3NyYy9ob29rcy91c2UtbW9iaWxlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcblxuZXhwb3J0IGNvbnN0IE1PQklMRV9CUkVBS1BPSU5UID0gODUwO1xuXG5leHBvcnQgZnVuY3Rpb24gdXNlSXNNb2JpbGUoKSB7XG4gIGNvbnN0IFtpc01vYmlsZSwgc2V0SXNNb2JpbGVdID0gdXNlU3RhdGU8Ym9vbGVhbj4oKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IG1xbCA9IHdpbmRvdy5tYXRjaE1lZGlhKGAobWF4LXdpZHRoOiAke01PQklMRV9CUkVBS1BPSU5UIC0gMX1weClgKTtcblxuICAgIGNvbnN0IG9uQ2hhbmdlID0gKCkgPT4ge1xuICAgICAgc2V0SXNNb2JpbGUod2luZG93LmlubmVyV2lkdGggPCBNT0JJTEVfQlJFQUtQT0lOVCk7XG4gICAgfTtcblxuICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgTU9CSUxFX0JSRUFLUE9JTlQpO1xuXG4gICAgbXFsLmFkZEV2ZW50TGlzdGVuZXIoXCJjaGFuZ2VcIiwgb25DaGFuZ2UpO1xuICAgIHJldHVybiAoKSA9PiBtcWwucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImNoYW5nZVwiLCBvbkNoYW5nZSk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4gISFpc01vYmlsZTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIk1PQklMRV9CUkVBS1BPSU5UIiwidXNlSXNNb2JpbGUiLCJpc01vYmlsZSIsInNldElzTW9iaWxlIiwibXFsIiwid2luZG93IiwibWF0Y2hNZWRpYSIsIm9uQ2hhbmdlIiwiaW5uZXJXaWR0aCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-mobile.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useNotifications.ts":
/*!***************************************!*\
  !*** ./src/hooks/useNotifications.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_apiClientEnhanced__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/apiClientEnhanced */ \"(ssr)/./src/lib/apiClientEnhanced.ts\");\n\n\n\nconst useNotifications = ()=>{\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        unread_count: 0,\n        total_count: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Fetch notifications with pagination\n    const fetchNotifications = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNotifications.useCallback[fetchNotifications]\": async (page = 1, limit = 10)=>{\n            if (!session?.accessToken) {\n                setError('Tidak terautentikasi');\n                return;\n            }\n            try {\n                setLoading(true);\n                setError(null);\n                const response = await _lib_apiClientEnhanced__WEBPACK_IMPORTED_MODULE_2__.apiClient.get(`/users/me/notifications?page=${page}&limit=${limit}`, {\n                    token: session.accessToken\n                });\n                if (page === 1) {\n                    setNotifications(response.notifications);\n                } else {\n                    setNotifications({\n                        \"useNotifications.useCallback[fetchNotifications]\": (prev)=>[\n                                ...prev,\n                                ...response.notifications\n                            ]\n                    }[\"useNotifications.useCallback[fetchNotifications]\"]);\n                }\n                setStats({\n                    unread_count: response.unread_count,\n                    total_count: response.total\n                });\n                return response;\n            } catch (err) {\n                const errorMessage = err.response?.data?.error || 'Gagal mengambil notifikasi';\n                setError(errorMessage);\n                throw new Error(errorMessage);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useNotifications.useCallback[fetchNotifications]\"], [\n        session?.accessToken\n    ]);\n    // Fetch notification stats only\n    const fetchStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNotifications.useCallback[fetchStats]\": async ()=>{\n            if (!session?.accessToken) return null;\n            try {\n                const response = await _lib_apiClientEnhanced__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/users/me/notifications/stats', {\n                    token: session.accessToken\n                });\n                setStats(response);\n                return response;\n            } catch (err) {\n                console.error('Failed to fetch notification stats:', err);\n                return null;\n            }\n        }\n    }[\"useNotifications.useCallback[fetchStats]\"], [\n        session?.accessToken\n    ]);\n    // Mark specific notifications as read\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNotifications.useCallback[markAsRead]\": async (notificationIds)=>{\n            if (!session?.accessToken) {\n                throw new Error('Tidak terautentikasi');\n            }\n            try {\n                await _lib_apiClientEnhanced__WEBPACK_IMPORTED_MODULE_2__.apiClient.post('/users/me/notifications/mark-read', {\n                    notification_ids: notificationIds\n                }, {\n                    token: session.accessToken\n                });\n                // Update local state\n                setNotifications({\n                    \"useNotifications.useCallback[markAsRead]\": (prev)=>prev.map({\n                            \"useNotifications.useCallback[markAsRead]\": (notif)=>notificationIds.includes(notif.id) ? {\n                                    ...notif,\n                                    is_read: true,\n                                    read_at: new Date().toISOString()\n                                } : notif\n                        }[\"useNotifications.useCallback[markAsRead]\"])\n                }[\"useNotifications.useCallback[markAsRead]\"]);\n                // Update stats\n                setStats({\n                    \"useNotifications.useCallback[markAsRead]\": (prev)=>({\n                            ...prev,\n                            unread_count: Math.max(0, prev.unread_count - notificationIds.length)\n                        })\n                }[\"useNotifications.useCallback[markAsRead]\"]);\n            } catch (err) {\n                const errorMessage = err.response?.data?.error || 'Gagal menandai notifikasi sebagai dibaca';\n                setError(errorMessage);\n                throw new Error(errorMessage);\n            }\n        }\n    }[\"useNotifications.useCallback[markAsRead]\"], [\n        session?.accessToken\n    ]);\n    // Mark all notifications as read\n    const markAllAsRead = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNotifications.useCallback[markAllAsRead]\": async ()=>{\n            if (!session?.accessToken) {\n                throw new Error('Tidak terautentikasi');\n            }\n            try {\n                await _lib_apiClientEnhanced__WEBPACK_IMPORTED_MODULE_2__.apiClient.post('/users/me/notifications/mark-all-read', {}, {\n                    token: session.accessToken\n                });\n                // Update local state\n                setNotifications({\n                    \"useNotifications.useCallback[markAllAsRead]\": (prev)=>prev.map({\n                            \"useNotifications.useCallback[markAllAsRead]\": (notif)=>({\n                                    ...notif,\n                                    is_read: true,\n                                    read_at: new Date().toISOString()\n                                })\n                        }[\"useNotifications.useCallback[markAllAsRead]\"])\n                }[\"useNotifications.useCallback[markAllAsRead]\"]);\n                // Update stats\n                setStats({\n                    \"useNotifications.useCallback[markAllAsRead]\": (prev)=>({\n                            ...prev,\n                            unread_count: 0\n                        })\n                }[\"useNotifications.useCallback[markAllAsRead]\"]);\n            } catch (err) {\n                const errorMessage = err.response?.data?.error || 'Gagal menandai semua notifikasi sebagai dibaca';\n                setError(errorMessage);\n                throw new Error(errorMessage);\n            }\n        }\n    }[\"useNotifications.useCallback[markAllAsRead]\"], [\n        session?.accessToken\n    ]);\n    // Delete notification\n    const deleteNotification = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNotifications.useCallback[deleteNotification]\": async (notificationId)=>{\n            if (!session?.accessToken) {\n                throw new Error('Tidak terautentikasi');\n            }\n            try {\n                await _lib_apiClientEnhanced__WEBPACK_IMPORTED_MODULE_2__.apiClient.delete(`/users/me/notifications/${notificationId}`, {\n                    token: session.accessToken\n                });\n                // Update local state\n                const deletedNotification = notifications.find({\n                    \"useNotifications.useCallback[deleteNotification].deletedNotification\": (n)=>n.id === notificationId\n                }[\"useNotifications.useCallback[deleteNotification].deletedNotification\"]);\n                setNotifications({\n                    \"useNotifications.useCallback[deleteNotification]\": (prev)=>prev.filter({\n                            \"useNotifications.useCallback[deleteNotification]\": (notif)=>notif.id !== notificationId\n                        }[\"useNotifications.useCallback[deleteNotification]\"])\n                }[\"useNotifications.useCallback[deleteNotification]\"]);\n                // Update stats\n                setStats({\n                    \"useNotifications.useCallback[deleteNotification]\": (prev)=>({\n                            total_count: Math.max(0, prev.total_count - 1),\n                            unread_count: deletedNotification && !deletedNotification.is_read ? Math.max(0, prev.unread_count - 1) : prev.unread_count\n                        })\n                }[\"useNotifications.useCallback[deleteNotification]\"]);\n            } catch (err) {\n                const errorMessage = err.response?.data?.error || 'Gagal menghapus notifikasi';\n                setError(errorMessage);\n                throw new Error(errorMessage);\n            }\n        }\n    }[\"useNotifications.useCallback[deleteNotification]\"], [\n        notifications,\n        session?.accessToken\n    ]);\n    // Auto-refresh stats periodically (polling approach)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useNotifications.useEffect\": ()=>{\n            // Initial fetch\n            fetchStats();\n            // Set up polling every 30 seconds\n            const interval = setInterval(fetchStats, 30000);\n            return ({\n                \"useNotifications.useEffect\": ()=>clearInterval(interval)\n            })[\"useNotifications.useEffect\"];\n        }\n    }[\"useNotifications.useEffect\"], [\n        fetchStats\n    ]);\n    return {\n        notifications,\n        stats,\n        loading,\n        error,\n        fetchNotifications,\n        fetchStats,\n        markAsRead,\n        markAllAsRead,\n        deleteNotification,\n        // Helper functions\n        hasUnread: stats.unread_count > 0,\n        unreadCount: stats.unread_count,\n        totalCount: stats.total_count\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useNotifications.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/apiClientEnhanced.ts":
/*!**************************************!*\
  !*** ./src/lib/apiClientEnhanced.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   apiClientLegacy: () => (/* binding */ apiClientLegacy),\n/* harmony export */   createApiClientFromRequest: () => (/* binding */ createApiClientFromRequest),\n/* harmony export */   createAuthenticatedApiClient: () => (/* binding */ createAuthenticatedApiClient),\n/* harmony export */   safeApiCall: () => (/* binding */ safeApiCall)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(ssr)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/jwt */ \"(ssr)/./node_modules/next-auth/jwt/index.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_jwt__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * Enhanced API Client dengan fitur tambahan untuk mengurangi pengulangan kode\n * dan meningkatkan maintainability\n */ \n\n\n// ===== ENHANCED API CLIENT =====\nclass ApiClient {\n    constructor(baseUrl, timeout = 10000){\n        this.baseUrl = baseUrl || \"http://localhost:8000/api/v1\" || 0;\n        this.defaultTimeout = timeout;\n    }\n    // Helper method untuk mendapatkan URL lengkap untuk foto\n    getPhotoUrl(photoPath, bustCache = false) {\n        if (!photoPath) {\n            return \"/images/user/user-03.png\"; // Default fallback\n        }\n        // Jika sudah URL lengkap, return as is\n        if (photoPath.startsWith('http')) {\n            return photoPath;\n        }\n        // Extract base URL dari API URL (hapus /api/v1)\n        const apiUrl = \"http://localhost:8000/api/v1\" || 0;\n        const baseUrl = apiUrl.replace('/api/v1', '');\n        let url = `${baseUrl}/${photoPath}`;\n        // Tambahkan cache busting jika diperlukan\n        if (bustCache) {\n            url += `?t=${Date.now()}`;\n        }\n        return url;\n    }\n    async makeRequest(path, options = {}) {\n        const { token, skipAuth, timeout, ...restOptions } = options;\n        const headers = new Headers(restOptions.headers || {});\n        // Set Content-Type jika belum ada dan bukan FormData\n        if (!headers.has('Content-Type') && restOptions.method !== 'GET' && !(restOptions.body instanceof FormData)) {\n            headers.set('Content-Type', 'application/json');\n        }\n        // Tambahkan token otentikasi jika tersedia dan tidak di-skip\n        if (token && !skipAuth) {\n            headers.set('Authorization', `Bearer ${token}`);\n        }\n        // Setup timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), timeout || this.defaultTimeout);\n        try {\n            const response = await fetch(`${this.baseUrl}${path}`, {\n                ...restOptions,\n                headers,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            const data = await response.json();\n            if (!response.ok) {\n                const error = new Error(data.error || data.message || `HTTP ${response.status}: ${response.statusText}`);\n                error.status = response.status;\n                error.details = data.details || null;\n                error.response = {\n                    data,\n                    status: response.status\n                };\n                throw error;\n            }\n            return data;\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error instanceof Error && error.name === 'AbortError') {\n                throw new Error('Request timeout');\n            }\n            throw error;\n        }\n    }\n    // ===== HTTP METHODS =====\n    async get(path, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'GET'\n        });\n    }\n    async post(path, data, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'POST',\n            body: data instanceof FormData ? data : data ? JSON.stringify(data) : undefined\n        });\n    }\n    async put(path, data, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async delete(path, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'DELETE'\n        });\n    }\n    async patch(path, data, options = {}) {\n        return this.makeRequest(path, {\n            ...options,\n            method: 'PATCH',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n}\n// ===== SINGLETON INSTANCE =====\nconst apiClient = new ApiClient();\n// ===== HELPER FUNCTIONS =====\n// Untuk Server Components\nasync function createAuthenticatedApiClient() {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session?.accessToken) {\n        throw new Error('Tidak terautentikasi atau token tidak ada.');\n    }\n    const client = new ApiClient();\n    const token = session.accessToken;\n    // Override methods dengan token yang sudah terikat\n    client.get = (path, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'GET',\n            token\n        });\n    client.post = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'POST',\n            body: data instanceof FormData ? data : data ? JSON.stringify(data) : undefined,\n            token\n        });\n    client.put = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined,\n            token\n        });\n    client.delete = (path, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'DELETE',\n            token\n        });\n    client.patch = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'PATCH',\n            body: data ? JSON.stringify(data) : undefined,\n            token\n        });\n    return client;\n}\n// Untuk API Routes\nasync function createApiClientFromRequest(req) {\n    const token = await (0,next_auth_jwt__WEBPACK_IMPORTED_MODULE_2__.getToken)({\n        req,\n        secret: process.env.NEXTAUTH_SECRET\n    });\n    if (!token?.accessToken) {\n        throw new Error('Tidak terautentikasi atau token tidak ada.');\n    }\n    const client = new ApiClient();\n    const accessToken = token.accessToken;\n    // Override methods dengan token yang sudah terikat\n    client.get = (path, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'GET',\n            token: accessToken\n        });\n    client.post = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'POST',\n            body: data instanceof FormData ? data : data ? JSON.stringify(data) : undefined,\n            token: accessToken\n        });\n    client.put = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined,\n            token: accessToken\n        });\n    client.delete = (path, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'DELETE',\n            token: accessToken\n        });\n    client.patch = (path, data, options = {})=>client.makeRequest(path, {\n            ...options,\n            method: 'PATCH',\n            body: data ? JSON.stringify(data) : undefined,\n            token: accessToken\n        });\n    return client;\n}\n// Error handling wrapper\nasync function safeApiCall(apiCall, fallback, onError) {\n    try {\n        return await apiCall();\n    } catch (error) {\n        const err = error instanceof Error ? error : new Error(String(error));\n        console.error('API call failed:', err.message);\n        if (onError) {\n            onError(err);\n        }\n        return fallback || null;\n    }\n}\n// ===== BACKWARD COMPATIBILITY =====\n// Untuk menjaga kompatibilitas dengan apiClient.ts yang sudah ada\nasync function apiClientLegacy(path, options = {}) {\n    return apiClient.makeRequest(path, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/apiClientEnhanced.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(ssr)/./node_modules/next-auth/providers/credentials.js\");\n\nconst authOptions = {\n    providers: [\n        // Provider untuk login email/password tradisional\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"credentials\",\n            name: \"Credentials\",\n            credentials: {\n                identifier: {\n                    label: \"Identifier\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials) {\n                    return null;\n                }\n                try {\n                    // Panggil endpoint login di backend Anda\n                    const res = await fetch(`${\"http://localhost:8000/api/v1\"}/auth/login`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            identifier: credentials.identifier,\n                            password: credentials.password\n                        })\n                    });\n                    const data = await res.json();\n                    // Jika login gagal, backend sekarang mengembalikan { user, accessToken }\n                    if (!res.ok || !data.user || !data.accessToken) {\n                        const errorMessage = data.error || \"Kredensial tidak valid atau terjadi kesalahan.\";\n                        throw new Error(errorMessage);\n                    }\n                    // Kembalikan objek yang akan disimpan di session JWT\n                    // Strukturnya sekarang sama dengan provider telegram\n                    return {\n                        ...data.user,\n                        accessToken: data.accessToken\n                    };\n                } catch (error) {\n                    // Log error untuk debugging di sisi server\n                    console.error(\"Authorize Error:\", error);\n                    // Lempar error agar bisa ditangkap oleh NextAuth dan ditampilkan ke pengguna\n                    throw new Error(error.message || \"Terjadi kesalahan saat mencoba masuk.\");\n                }\n            }\n        }),\n        // Provider untuk login via Telegram\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"telegram\",\n            name: \"Telegram\",\n            credentials: {\n                user_data: {\n                    label: \"User Data\",\n                    type: \"text\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.user_data) {\n                    return null;\n                }\n                try {\n                    const telegramUserData = JSON.parse(credentials.user_data);\n                    const res = await fetch(`${\"http://localhost:8000/api/v1\"}/auth/telegram`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(telegramUserData)\n                    });\n                    const responseData = await res.json();\n                    if (res.ok && responseData && responseData.user && responseData.accessToken) {\n                        // Backend mengembalikan { user: {...}, accessToken: \"...\" }\n                        // Kita harus mengembalikan objek yang sama agar NextAuth dapat memprosesnya di callback jwt\n                        return responseData;\n                    }\n                    // Jika backend mengembalikan error atau formatnya salah, log error tersebut\n                    console.error(\"Telegram auth failed:\", responseData.error || \"Unknown error from backend\");\n                    return null;\n                } catch (error) {\n                    console.error(\"Error in Telegram authorize function:\", error);\n                    // Melempar error agar bisa ditangkap oleh frontend jika diperlukan\n                    throw new Error(error.message || \"An unexpected error occurred during Telegram login.\");\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            // Saat login pertama kali, objek 'user' dari provider tersedia.\n            // Kita salin accessToken dan data user ke dalam token JWT.\n            if (user) {\n                return {\n                    ...token,\n                    accessToken: user.accessToken,\n                    user: user\n                };\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Setiap kali sesi diakses, data dari token disalin ke objek sesi.\n            // Ini membuat data tersedia di sisi client melalui useSession() atau getServerSession().\n            session.user = token.user;\n            session.accessToken = token.accessToken;\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/'\n    },\n    session: {\n        strategy: \"jwt\",\n        // Session expires in 30 minutes, matching the backend token lifetime.\n        maxAge: parseInt(process.env.NEXTAUTH_SESSION_MAX_AGE || '1800', 10)\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(ssr)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/locale/id.js\");\n\n\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(date, 'dd MMMM yyyy', {\n        locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.id\n    });\n}\nfunction formatDateTime(date) {\n    return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(date, 'dd MMMM yyyy HH:mm', {\n        locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.id\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEM7QUFDSjtBQUNQO0FBQ0c7QUFFN0IsU0FBU0ksR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPSix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNLO0FBQ3RCO0FBRU8sU0FBU0MsV0FBV0MsSUFBVTtJQUNuQyxPQUFPTCw4RUFBTUEsQ0FBQ0ssTUFBTSxnQkFBZ0I7UUFBRUMsUUFBUUwsK0NBQUVBO0lBQUM7QUFDbkQ7QUFFTyxTQUFTTSxlQUFlRixJQUFVO0lBQ3ZDLE9BQU9MLDhFQUFNQSxDQUFDSyxNQUFNLHNCQUFzQjtRQUFFQyxRQUFRTCwrQ0FBRUE7SUFBQztBQUN6RCIsInNvdXJjZXMiOlsiL2hvbWUvaW5zb21uaWEvcHJvamVjdC92cG4tc2hvcC9mcm9uZGVuZC9zcmMvbGliL3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuaW1wb3J0IHsgZm9ybWF0IH0gZnJvbSBcImRhdGUtZm5zXCJcbmltcG9ydCB7IGlkIH0gZnJvbSBcImRhdGUtZm5zL2xvY2FsZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXREYXRlKGRhdGU6IERhdGUpOiBzdHJpbmcge1xuICByZXR1cm4gZm9ybWF0KGRhdGUsICdkZCBNTU1NIHl5eXknLCB7IGxvY2FsZTogaWQgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGVUaW1lKGRhdGU6IERhdGUpOiBzdHJpbmcge1xuICByZXR1cm4gZm9ybWF0KGRhdGUsICdkZCBNTU1NIHl5eXkgSEg6bW0nLCB7IGxvY2FsZTogaWQgfSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImZvcm1hdCIsImlkIiwiY24iLCJpbnB1dHMiLCJmb3JtYXREYXRlIiwiZGF0ZSIsImxvY2FsZSIsImZvcm1hdERhdGVUaW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/css/badge.css":
/*!***************************!*\
  !*** ./src/css/badge.css ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0336ecfeded1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL2JhZGdlLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiL2hvbWUvaW5zb21uaWEvcHJvamVjdC92cG4tc2hvcC9mcm9uZGVuZC9zcmMvY3NzL2JhZGdlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjAzMzZlY2ZlZGVkMVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/css/badge.css\n");

/***/ }),

/***/ "(ssr)/./src/css/card.css":
/*!**************************!*\
  !*** ./src/css/card.css ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5f2129dddb68\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL2NhcmQuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL3NyYy9jc3MvY2FyZC5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1ZjIxMjlkZGRiNjhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/css/card.css\n");

/***/ }),

/***/ "(ssr)/./src/css/form.css":
/*!**************************!*\
  !*** ./src/css/form.css ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"87c72a41abe5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL2Zvcm0uY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL3NyYy9jc3MvZm9ybS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4N2M3MmE0MWFiZTVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/css/form.css\n");

/***/ }),

/***/ "(ssr)/./src/css/notification.css":
/*!**********************************!*\
  !*** ./src/css/notification.css ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ad5ded71216d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL25vdGlmaWNhdGlvbi5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9ob21lL2luc29tbmlhL3Byb2plY3QvdnBuLXNob3AvZnJvbmRlbmQvc3JjL2Nzcy9ub3RpZmljYXRpb24uY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYWQ1ZGVkNzEyMTZkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/css/notification.css\n");

/***/ }),

/***/ "(ssr)/./src/css/satoshi.css":
/*!*****************************!*\
  !*** ./src/css/satoshi.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1193134113d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL3NhdG9zaGkuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL3NyYy9jc3Mvc2F0b3NoaS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxMTkzMTM0MTEzZDlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/css/satoshi.css\n");

/***/ }),

/***/ "(ssr)/./src/css/style.css":
/*!***************************!*\
  !*** ./src/css/style.css ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d25285bf284a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL3N0eWxlLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiL2hvbWUvaW5zb21uaWEvcHJvamVjdC92cG4tc2hvcC9mcm9uZGVuZC9zcmMvY3NzL3N0eWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQyNTI4NWJmMjg0YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/css/style.css\n");

/***/ }),

/***/ "(ssr)/./src/css/tabs.css":
/*!**************************!*\
  !*** ./src/css/tabs.css ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2eaf9073c4af\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL3RhYnMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvaG9tZS9pbnNvbW5pYS9wcm9qZWN0L3Zwbi1zaG9wL2Zyb25kZW5kL3NyYy9jc3MvdGFicy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyZWFmOTA3M2M0YWZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/css/tabs.css\n");

/***/ }),

/***/ "(rsc)/./src/app/(dashboard)/notifications/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/(dashboard)/notifications/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/project/vpn-shop/frondend/src/app/(dashboard)/notifications/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/project/vpn-shop/frondend/src/app/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n\n\n\nconst NotFound = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen flex-col items-center justify-center bg-white dark:bg-gray-dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-1 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-1/2 top-1/2 -z-1 -translate-x-1/2 -translate-y-1/2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/images/grids/grid-01.svg\",\n                        alt: \"grid\",\n                        width: 575,\n                        height: 460,\n                        className: \"dark:opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto mb-10 flex h-28 w-28 items-center justify-center rounded-full border border-stroke bg-white text-dark shadow-error dark:border-dark-3 dark:bg-dark-2 dark:text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"50\",\n                        height: \"51\",\n                        viewBox: \"0 0 50 51\",\n                        fill: \"currentColor\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                clipRule: \"evenodd\",\n                                d: \"M24.9993 6.26758C14.3564 6.26758 5.72852 14.8954 5.72852 25.5384C5.72852 36.1814 14.3564 44.8092 24.9993 44.8092C35.6423 44.8092 44.2702 36.1814 44.2702 25.5384C44.2702 14.8954 35.6423 6.26758 24.9993 6.26758ZM2.60352 25.5384C2.60352 13.1695 12.6305 3.14258 24.9993 3.14258C37.3682 3.14258 47.3952 13.1695 47.3952 25.5384C47.3952 37.9073 37.3682 47.9342 24.9993 47.9342C12.6305 47.9342 2.60352 37.9073 2.60352 25.5384ZM17.8189 34.6998C19.8448 33.1982 22.3223 32.3092 24.9993 32.3092C27.6764 32.3092 30.1539 33.1982 32.1798 34.6998C32.8731 35.2137 33.0185 36.1923 32.5046 36.8855C31.9907 37.5788 31.0122 37.7242 30.3189 37.2103C28.8015 36.0856 26.97 35.4342 24.9993 35.4342C23.0287 35.4342 21.1972 36.0856 19.6798 37.2103C18.9865 37.7242 18.008 37.5788 17.4941 36.8855C16.9802 36.1923 17.1256 35.2137 17.8189 34.6998Z\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M33.3327 22.4134C33.3327 24.1393 32.3999 25.5384 31.2493 25.5384C30.0988 25.5384 29.166 24.1393 29.166 22.4134C29.166 20.6875 30.0988 19.2884 31.2493 19.2884C32.3999 19.2884 33.3327 20.6875 33.3327 22.4134Z\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M20.8327 22.4134C20.8327 24.1393 19.8999 25.5384 18.7493 25.5384C17.5988 25.5384 16.666 24.1393 16.666 22.4134C16.666 20.6875 17.5988 19.2884 18.7493 19.2884C19.8999 19.2884 20.8327 20.6875 20.8327 22.4134Z\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"mb-5 text-2xl font-black text-dark dark:text-white sm:text-4xl\",\n                    children: \"Page Not Found\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mx-auto max-w-sm font-medium\",\n                    children: \"The page you are looking for doesn’t exist. Here are some helpful links:\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/\",\n                    className: \"mt-8 inline-flex items-center gap-2 rounded-md bg-primary px-6 py-3 font-medium text-white hover:bg-opacity-90\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"18\",\n                            height: \"18\",\n                            viewBox: \"0 0 18 18\",\n                            fill: \"currentColor\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M15.7492 8.38125H3.73984L8.52109 3.51562C8.77422 3.2625 8.77422 2.86875 8.52109 2.61562C8.26797 2.3625 7.87422 2.3625 7.62109 2.61562L1.79922 8.52187C1.54609 8.775 1.54609 9.16875 1.79922 9.42188L7.62109 15.3281C7.73359 15.4406 7.90234 15.525 8.07109 15.525C8.23984 15.525 8.38047 15.4687 8.52109 15.3562C8.77422 15.1031 8.77422 14.7094 8.52109 14.4563L3.76797 9.64687H15.7492C16.0867 9.64687 16.368 9.36562 16.368 9.02812C16.368 8.6625 16.0867 8.38125 15.7492 8.38125Z\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Back to Home\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/project/vpn-shop/frondend/src/app/not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotFound);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9ob21lL2luc29tbmlhL3Byb2plY3QvdnBuLXNob3AvZnJvbmRlbmQvc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/@babel","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/date-fns","vendor-chunks/tailwind-merge","vendor-chunks/prop-types","vendor-chunks/nprogress","vendor-chunks/nextjs-toploader","vendor-chunks/react-is","vendor-chunks/next-themes","vendor-chunks/object-assign","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/jsvectormap","vendor-chunks/flatpickr"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fnotifications%2Fpage&page=%2F(dashboard)%2Fnotifications%2Fpage&appPaths=%2F(dashboard)%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fnotifications%2Fpage.tsx&appDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Finsomnia%2Fproject%2Fvpn-shop%2Ffrondend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();