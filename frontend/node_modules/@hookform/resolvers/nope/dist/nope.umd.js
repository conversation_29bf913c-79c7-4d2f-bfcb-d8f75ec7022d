!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?o(exports,require("@hookform/resolvers")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers"],o):o((e||self).hookformResolversNope={},e.hookformResolvers)}(this,function(e,o){var r=function(e,o,t){return void 0===o&&(o={}),void 0===t&&(t=""),Object.keys(e).reduce(function(o,s){var n=t?t+"."+s:s,i=e[s];return"string"==typeof i?o[n]={message:i}:r(i,o,n),o},o)};e.nopeResolver=function(e,t){return void 0===t&&(t={abortEarly:!1}),function(s,n,i){var f=e.validate(s,n,t);return f?{values:{},errors:o.toNestErrors(r(f),i)}:(i.shouldUseNativeValidation&&o.validateFieldsNatively({},i),{values:s,errors:{}})}}});
//# sourceMappingURL=nope.umd.js.map
