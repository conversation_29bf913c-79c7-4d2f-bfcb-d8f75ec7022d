{"version": 3, "file": "computed-types.umd.js", "sources": ["../src/computed-types.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { ValidationError } from 'computed-types';\nimport FunctionType from 'computed-types/lib/schema/FunctionType';\nimport type { FieldErrors, FieldValues, Resolver } from 'react-hook-form';\n\nconst isValidationError = (error: any): error is ValidationError =>\n  error.errors != null;\n\nfunction parseErrorSchema(computedTypesError: ValidationError) {\n  const parsedErrors: FieldErrors = {};\n  return (computedTypesError.errors || []).reduce((acc, error) => {\n    acc[error.path.join('.')] = {\n      type: error.error.name,\n      message: error.error.message,\n    };\n\n    return acc;\n  }, parsedErrors);\n}\n\n/**\n * Creates a resolver for react-hook-form using computed-types schema validation\n * @param {Schema} schema - The computed-types schema to validate against\n * @returns {Resolver<Type<typeof schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = Schema({\n *   name: string,\n *   age: number\n * });\n *\n * useForm({\n *   resolver: computedTypesResolver(schema)\n * });\n */\nexport function computedTypesResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n>(schema: FunctionType<Output, [Input]>): Resolver<Input, Context, Output> {\n  return async (values, _, options) => {\n    try {\n      const data = await schema(values);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {},\n        values: data,\n      };\n    } catch (error: any) {\n      if (isValidationError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(parseErrorSchema(error), options),\n        };\n      }\n\n      throw error;\n    }\n  };\n}\n"], "names": ["schema", "values", "_", "options", "Promise", "resolve", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "errors", "_catch", "error", "isValidationError", "toNestErrors", "computedTypesError", "reduce", "acc", "path", "join", "type", "name", "message", "e", "reject"], "mappings": "2VAkCM,SAIJA,GACA,OAAcC,SAAAA,EAAQC,EAAGC,GAAW,IAAA,OAAAC,QAAAC,gCAC9BD,QAAAC,QACiBL,EAAOC,IAAOK,KAAA,SAA3BC,GAIN,OAFAJ,EAAQK,2BAA6BC,EAAAA,uBAAuB,CAAA,EAAIN,GAEzD,CACLO,OAAQ,CAAA,EACRT,OAAQM,EACR,4DAT8BI,CAAA,EAUjC,SAAQC,GACP,GA7CoB,SAACA,GACzB,OAAgB,MAAhBA,EAAMF,MAAc,CA4CZG,CAAkBD,GACpB,MAAO,CACLX,OAAQ,CAAE,EACVS,OAAQI,gBA7CQC,EA6CsBH,GA3CtCG,EAAmBL,QAAU,IAAIM,OAAO,SAACC,EAAKL,GAMpD,OALAK,EAAIL,EAAMM,KAAKC,KAAK,MAAQ,CAC1BC,KAAMR,EAAMA,MAAMS,KAClBC,QAASV,EAAMA,MAAMU,SAGhBL,CACT,EARkC,CAAE,IA4CkBd,IA7CxD,IAA0BY,EAiDpB,MAAMH,CACR,GACF,CAAC,MAAAW,GAAAnB,OAAAA,QAAAoB,OAAAD,EACH,CAAA,CAAA"}