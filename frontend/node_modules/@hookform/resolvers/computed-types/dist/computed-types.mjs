import{validateFieldsNatively as r,toNestErrors as e}from"@hookform/resolvers";function t(t){return function(n,o,u){try{return Promise.resolve(function(e,o){try{var s=Promise.resolve(t(n)).then(function(e){return u.shouldUseNativeValidation&&r({},u),{errors:{},values:e}})}catch(r){return o(r)}return s&&s.then?s.then(void 0,o):s}(0,function(r){if(function(r){return null!=r.errors}(r))return{values:{},errors:e((t=r,(t.errors||[]).reduce(function(r,e){return r[e.path.join(".")]={type:e.error.name,message:e.error.message},r},{})),u)};var t;throw r}))}catch(r){return Promise.reject(r)}}}export{t as computedTypesResolver};
//# sourceMappingURL=computed-types.module.js.map
