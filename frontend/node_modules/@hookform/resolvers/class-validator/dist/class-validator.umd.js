!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("class-transformer"),require("class-validator")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","class-transformer","class-validator"],r):r((e||self).hookformResolversClassValidator={},e.hookformResolvers,e.classTransformer,e.classValidator)}(this,function(e,r,o,s){function t(e,r,o,s){return void 0===o&&(o={}),void 0===s&&(s=""),e.reduce(function(e,o){var a=s?s+"."+o.property:o.property;if(o.constraints){var i=Object.keys(o.constraints)[0];e[a]={type:i,message:o.constraints[i]};var n=e[a];r&&n&&Object.assign(n,{types:o.constraints})}return o.children&&o.children.length&&t(o.children,r,e,a),e},o)}e.classValidatorResolver=function(e,a,i){return void 0===a&&(a={}),void 0===i&&(i={}),function(n,l,c){try{var d=a.validator,f=o.plainToClass(e,n,a.transformer);return Promise.resolve(("sync"===i.mode?s.validateSync:s.validate)(f,d)).then(function(e){return e.length?{values:{},errors:r.toNestErrors(t(e,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)}:(c.shouldUseNativeValidation&&r.validateFieldsNatively({},c),{values:i.raw?Object.assign({},n):f,errors:{}})})}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=class-validator.umd.js.map
