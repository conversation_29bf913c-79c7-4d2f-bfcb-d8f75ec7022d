const e=[];class t{constructor(e,t,i,n,s,r,o){this.id=e,this.parent=t,this.child=i,this.thiz=n,this.data=s,this.args=r,this.fn=o}getNewTarget(e){const t=this.thiz;return null==t||null==t.constructor?0:t instanceof this.fn?e.ensureHandleId(t.constructor):0}dispose(){void 0!==this.thiz&&(this.thiz=void 0),this.args=e,this.fn=null}}const i=new t(0,null,null,null,0,null,null);class n{constructor(){this.current=i}get(e){if(1===e)return i.child;let t=i;for(let i=0;i<e;++i)if(t=t.child,null===t)return null;return t===i?null:t}pop(){const e=this.current;e!==i&&(this.current=e.parent,e.dispose())}push(e,i,n,s){let r=this.current.child;return r?(r.thiz=e,r.data=i,r.args=n,r.fn=s):(r=new t(this.current.id+1,this.current,null,e,i,n,s),this.current.child=r),this.current=r,r.id}dispose(){this.current=null}}const s=function(){let e;try{e=new Function}catch(e){return!1}return"function"==typeof e}(),r=function(){if("undefined"!=typeof globalThis)return globalThis;let e=function(){return this}();if(!e&&s)try{e=new Function("return this")()}catch(e){}if(!e){if("undefined"==typeof __webpack_public_path__&&"undefined"!=typeof global)return global;if("undefined"!=typeof window)return window;if("undefined"!=typeof self)return self}return e}();class o{constructor(){this._exception=void 0,this._caught=!1}isEmpty(){return!this._caught}hasCaught(){return this._caught}exception(){return this._exception}setError(e){this._caught=!0,this._exception=e}reset(){this._caught=!1,this._exception=void 0}extractException(){const e=this._exception;return this.reset(),e}}const a=function(){var e;try{return Boolean(null===(e=Object.getOwnPropertyDescriptor(Function.prototype,"name"))||void 0===e?void 0:e.configurable)}catch(e){return!1}}(),l="object"==typeof Reflect,c="undefined"!=typeof FinalizationRegistry&&"undefined"!=typeof WeakRef,u=function(){try{const e=Symbol();new WeakRef(e),(new WeakMap).set(e,void 0)}catch(e){return!1}return!0}(),h="undefined"!=typeof BigInt;function p(e){return"object"==typeof e&&null!==e||"function"==typeof e}const d=function(){let e;return e="undefined"!=typeof __webpack_public_path__||"undefined"!=typeof __webpack_public_path__?"undefined"!=typeof __non_webpack_require__?__non_webpack_require__:void 0:"undefined"!=typeof require?require:void 0,e}(),f="function"==typeof MessageChannel?MessageChannel:function(){try{return d("worker_threads").MessageChannel}catch(e){}}(),_="function"==typeof setImmediate?setImmediate:function(e){if("function"!=typeof e)throw new TypeError('The "callback" argument must be of type function');if(f){let t=new f;t.port1.onmessage=function(){t.port1.onmessage=null,t=void 0,e()},t.port2.postMessage(null)}else setTimeout(e,0)},v="function"==typeof Buffer?Buffer:function(){try{return d("buffer").Buffer}catch(e){}}(),g="1.3.1",y=1,z=9,b=2147483647,k=8;class w{constructor(e,t){this.id=e,this.value=t}data(e){return e.getObjectBinding(this.value).data}isNumber(){return"number"==typeof this.value}isBigInt(){return"bigint"==typeof this.value}isString(){return"string"==typeof this.value}isFunction(){return"function"==typeof this.value}isExternal(){return p(this.value)&&null===Object.getPrototypeOf(this.value)}isObject(){return"object"==typeof this.value&&null!==this.value}isArray(){return Array.isArray(this.value)}isArrayBuffer(){return this.value instanceof ArrayBuffer}isTypedArray(){return ArrayBuffer.isView(this.value)&&!(this.value instanceof DataView)}isBuffer(e){return!!ArrayBuffer.isView(this.value)||(null!=e||(e=v),"function"==typeof e&&e.isBuffer(this.value))}isDataView(){return this.value instanceof DataView}isDate(){return this.value instanceof Date}isPromise(){return this.value instanceof Promise}isBoolean(){return"boolean"==typeof this.value}isUndefined(){return void 0===this.value}isSymbol(){return"symbol"==typeof this.value}isNull(){return null===this.value}dispose(){this.value=void 0}}class m extends w{constructor(e,t){super(e,t)}dispose(){}}function S(){Object.setPrototypeOf(this,null)}S.prototype=null;class C{constructor(){this._values=[void 0,C.UNDEFINED,C.NULL,C.FALSE,C.TRUE,C.GLOBAL],this._next=C.MIN_ID}push(e){let t;const i=this._next,n=this._values;return i<n.length?(t=n[i],t.value=e):(t=new w(i,e),n[i]=t),this._next++,t}erase(e,t){this._next=e;const i=this._values;for(let n=e;n<t;++n)i[n].dispose()}get(e){return this._values[e]}swap(e,t){const i=this._values,n=i[e];i[e]=i[t],i[e].id=Number(e),i[t]=n,n.id=Number(t)}dispose(){this._values.length=C.MIN_ID,this._next=C.MIN_ID}}C.UNDEFINED=new m(1,void 0),C.NULL=new m(2,null),C.FALSE=new m(3,!1),C.TRUE=new m(4,!0),C.GLOBAL=new m(5,r),C.MIN_ID=6;class x{constructor(e,t,i,n,s=n){this.handleStore=e,this.id=t,this.parent=i,this.child=null,null!==i&&(i.child=this),this.start=n,this.end=s,this._escapeCalled=!1}add(e){const t=this.handleStore.push(e);return this.end++,t}addExternal(e,t){const i=new S,n=e.ctx.handleStore.push(i);return e.initObjectBinding(i).data=t,this.end++,n}dispose(){this.start!==this.end&&this.handleStore.erase(this.start,this.end)}escape(e){if(this._escapeCalled)return null;if(this._escapeCalled=!0,e<this.start||e>=this.end)return null;this.handleStore.swap(e,this.start);const t=this.handleStore.get(this.start);return this.start++,this.parent.end++,t}escapeCalled(){return this._escapeCalled}}class F{constructor(){this._rootScope=new x(null,0,null,1,C.MIN_ID),this.currentScope=this._rootScope}get(e){e=Number(e);let t=this.currentScope;for(;t!==this._rootScope;){if(t.id===e)return t;t=t.parent}}openScope(e){const t=this.currentScope;let i=t.child;return null!==i?(i.start=i.end=t.end,i._escapeCalled=!1):i=new x(e.ctx.handleStore,t.id+1,t,t.end),this.currentScope=i,e.openHandleScopes++,i}closeScope(e){if(0===e.openHandleScopes)return;const t=this.currentScope;this.currentScope=t.parent,t.dispose(),e.openHandleScopes--}dispose(){let e=this.currentScope;for(;null!==e;){e.handleStore=null,e.id=0,e.parent=null,e.start=C.MIN_ID,e.end=C.MIN_ID,e._escapeCalled=!1;const t=e.child;e.child=null,e=t}this.currentScope=null}}class E{constructor(){this._next=null,this._prev=null}dispose(){}finalize(){}link(e){this._prev=e,this._next=e._next,null!==this._next&&(this._next._prev=this),e._next=this}unlink(){null!==this._prev&&(this._prev._next=this._next),null!==this._next&&(this._next._prev=this._prev),this._prev=null,this._next=null}static finalizeAll(e){for(;null!==e._next;)e._next.finalize()}}class I{constructor(e,t=0,i=0,n=0){this.envObject=e,this._finalizeCallback=t,this._finalizeData=i,this._finalizeHint=n,this._makeDynCall_vppp=e.makeDynCall_vppp}callback(){return this._finalizeCallback}data(){return this._finalizeData}hint(){return this._finalizeHint}resetEnv(){this.envObject=void 0}resetFinalizer(){this._finalizeCallback=0,this._finalizeData=0,this._finalizeHint=0}callFinalizer(){const e=this._finalizeCallback,t=this._finalizeData,i=this._finalizeHint;if(this.resetFinalizer(),!e)return;const n=Number(e);this.envObject?this.envObject.callFinalizer(n,t,i):this._makeDynCall_vppp(n)(0,t,i)}dispose(){this.envObject=void 0,this._makeDynCall_vppp=void 0}}class D extends E{static create(e,t,i,n){const s=new D(e,t,i,n);return s.link(e.finalizing_reflist),s}constructor(e,t,i,n){super(),this._finalizer=new I(e,t,i,n)}data(){return this._finalizer.data()}dispose(){this._finalizer&&(this.unlink(),this._finalizer.envObject.dequeueFinalizer(this),this._finalizer.dispose(),this._finalizer=void 0,super.dispose())}finalize(){let e;this.unlink();let t=!1;try{this._finalizer.callFinalizer()}catch(i){t=!0,e=i}if(this.dispose(),t)throw e}}function O(e,t){if(!e.terminatedOrTerminating())throw t}class j{constructor(e,t,i,n,s){this.ctx=e,this.moduleApiVersion=t,this.makeDynCall_vppp=i,this.makeDynCall_vp=n,this.abort=s,this.openHandleScopes=0,this.instanceData=null,this.tryCatch=new o,this.refs=1,this.reflist=new E,this.finalizing_reflist=new E,this.pendingFinalizers=[],this.lastError={errorCode:0,engineErrorCode:0,engineReserved:0},this.inGcFinalizer=!1,this._bindingMap=new WeakMap,this.id=0}canCallIntoJs(){return!0}terminatedOrTerminating(){return!this.canCallIntoJs()}ref(){this.refs++}unref(){this.refs--,0===this.refs&&this.dispose()}ensureHandle(e){return this.ctx.ensureHandle(e)}ensureHandleId(e){return this.ensureHandle(e).id}clearLastError(){const e=this.lastError;return 0!==e.errorCode&&(e.errorCode=0),0!==e.engineErrorCode&&(e.engineErrorCode=0),0!==e.engineReserved&&(e.engineReserved=0),0}setLastError(e,t=0,i=0){const n=this.lastError;return n.errorCode!==e&&(n.errorCode=e),n.engineErrorCode!==t&&(n.engineErrorCode=t),n.engineReserved!==i&&(n.engineReserved=i),e}getReturnStatus(){return this.tryCatch.hasCaught()?this.setLastError(10):0}callIntoModule(e,t=O){const i=this.openHandleScopes;this.clearLastError();const n=e(this);if(i!==this.openHandleScopes&&this.abort("open_handle_scopes != open_handle_scopes_before"),this.tryCatch.hasCaught()){t(this,this.tryCatch.extractException())}return n}invokeFinalizerFromGC(e){if(this.moduleApiVersion!==b)this.enqueueFinalizer(e);else{const t=this.inGcFinalizer;this.inGcFinalizer=!0;try{e.finalize()}finally{this.inGcFinalizer=t}}}checkGCAccess(){this.moduleApiVersion===b&&this.inGcFinalizer&&this.abort("Finalizer is calling a function that may affect GC state.\nThe finalizers are run directly from GC and must not affect GC state.\nUse `node_api_post_finalizer` from inside of the finalizer to work around this issue.\nIt schedules the call as a new task in the event loop.")}enqueueFinalizer(e){-1===this.pendingFinalizers.indexOf(e)&&this.pendingFinalizers.push(e)}dequeueFinalizer(e){const t=this.pendingFinalizers.indexOf(e);-1!==t&&this.pendingFinalizers.splice(t,1)}deleteMe(){E.finalizeAll(this.finalizing_reflist),E.finalizeAll(this.reflist),this.tryCatch.extractException(),this.ctx.envStore.remove(this.id)}dispose(){0!==this.id&&(this.deleteMe(),this.finalizing_reflist.dispose(),this.reflist.dispose(),this.id=0)}initObjectBinding(e){const t={wrapped:0,tag:null,data:0};return this._bindingMap.set(e,t),t}getObjectBinding(e){return this._bindingMap.has(e)?this._bindingMap.get(e):this.initObjectBinding(e)}setInstanceData(e,t,i){this.instanceData&&this.instanceData.dispose(),this.instanceData=D.create(this,t,e,i)}getInstanceData(){return this.instanceData?this.instanceData.data():0}}class N extends j{constructor(e,t,i,n,s,r,o){super(e,i,n,s,r),this.filename=t,this.nodeBinding=o,this.destructing=!1,this.finalizationScheduled=!1}deleteMe(){this.destructing=!0,this.drainFinalizerQueue(),super.deleteMe()}canCallIntoJs(){return super.canCallIntoJs()&&this.ctx.canCallIntoJs()}triggerFatalException(e){if(this.nodeBinding)this.nodeBinding.napi.fatalException(e);else{if("object"!=typeof process||null===process||"function"!=typeof process._fatalException)throw e;process._fatalException(e)||(console.error(e),process.exit(1))}}callbackIntoModule(e,t){return this.callIntoModule(t,((t,i)=>{if(t.terminatedOrTerminating())return;const n="object"==typeof process&&null!==process,s=!!n&&Boolean(process.execArgv&&-1!==process.execArgv.indexOf("--force-node-api-uncaught-exceptions-policy"));if(t.moduleApiVersion<b&&!s&&!e){(n&&"function"==typeof process.emitWarning?process.emitWarning:function(e,t,i){if(e instanceof Error)console.warn(e.toString());else{const n=i?`[${i}] `:"";console.warn(`${n}${t||"Warning"}: ${e}`)}})("Uncaught N-API callback exception detected, please run node with option --force-node-api-uncaught-exceptions-policy=true to handle those exceptions properly.","DeprecationWarning","DEP0168")}else t.triggerFatalException(i)}))}callFinalizer(e,t,i){this.callFinalizerInternal(1,e,t,i)}callFinalizerInternal(e,t,i,n){const s=this.makeDynCall_vppp(t),r=this.id,o=this.ctx.openScope(this);try{this.callbackIntoModule(Boolean(e),(()=>{s(r,i,n)}))}finally{this.ctx.closeScope(this,o)}}enqueueFinalizer(e){super.enqueueFinalizer(e),this.finalizationScheduled||this.destructing||(this.finalizationScheduled=!0,this.ref(),_((()=>{this.finalizationScheduled=!1,this.unref(),this.drainFinalizerQueue()})))}drainFinalizerQueue(){for(;this.pendingFinalizers.length>0;){this.pendingFinalizers.shift().finalize()}}}function H(e,t,i,n,s,r,o){(i="number"!=typeof i?8:i)<8?i=8:i>9&&i!==b&&function(e,t){throw new Error(`${e} requires Node-API version ${t}, but this version of Node.js only supports version 9 add-ons.`)}(t,i);const a=new N(e,t,i,n,s,r,o);return e.envStore.add(a),e.addCleanupHook(a,(()=>{a.unref()}),0),a}class R extends Error{constructor(e){super(e);const t=new.target,i=t.prototype;if(!(this instanceof R)){const e=Object.setPrototypeOf;"function"==typeof e?e.call(Object,this,i):this.__proto__=i,"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,t)}}}Object.defineProperty(R.prototype,"name",{configurable:!0,writable:!0,value:"EmnapiError"});class B extends R{constructor(e,t){super(`${e}: The current runtime does not support "FinalizationRegistry" and "WeakRef".${t?` ${t}`:""}`)}}Object.defineProperty(B.prototype,"name",{configurable:!0,writable:!0,value:"NotSupportWeakRefError"});class A extends R{constructor(e,t){super(`${e}: The current runtime does not support "Buffer". Consider using buffer polyfill to make sure \`globalThis.Buffer\` is defined.${t?` ${t}`:""}`)}}Object.defineProperty(A.prototype,"name",{configurable:!0,writable:!0,value:"NotSupportBufferError"});class M{constructor(e){this._value=e}deref(){return this._value}dispose(){this._value=void 0}}class W{constructor(e){this._ref=new M(e)}setWeak(e,t){if(!c||void 0===this._ref||this._ref instanceof WeakRef)return;const i=this._ref.deref();try{W._registry.register(i,this,this);const n=new WeakRef(i);this._ref.dispose(),this._ref=n,this._param=e,this._callback=t}catch(e){if("symbol"!=typeof i)throw e}}clearWeak(){if(c&&void 0!==this._ref&&this._ref instanceof WeakRef){try{W._registry.unregister(this)}catch(e){}this._param=void 0,this._callback=void 0;const e=this._ref.deref();this._ref=void 0===e?e:new M(e)}}reset(){if(c)try{W._registry.unregister(this)}catch(e){}this._param=void 0,this._callback=void 0,this._ref instanceof M&&this._ref.dispose(),this._ref=void 0}isEmpty(){return void 0===this._ref}deref(){if(void 0!==this._ref)return this._ref.deref()}}var L;W._registry=c?new FinalizationRegistry((e=>{e._ref=void 0;const t=e._callback,i=e._param;e._callback=void 0,e._param=void 0,"function"==typeof t&&t(i)})):void 0,function(e){e[e.kRuntime=0]="kRuntime",e[e.kUserland=1]="kUserland"}(L||(L={}));class T extends E{static weakCallback(e){e.persistent.reset(),e.invokeFinalizerFromGC()}static create(e,t,i,n,s,r,o){const a=new T(e,t,i,n);return e.ctx.refStore.add(a),a.link(e.reflist),a}constructor(e,t,i,n){super(),this.envObject=e,this._refcount=i,this._ownership=n;const s=e.ctx.handleStore.get(t);var r;this.canBeWeak=(r=s).isObject()||r.isFunction()||r.isSymbol(),this.persistent=new W(s.value),this.id=0,0===i&&this._setWeak()}ref(){return this.persistent.isEmpty()?0:(1==++this._refcount&&this.canBeWeak&&this.persistent.clearWeak(),this._refcount)}unref(){return this.persistent.isEmpty()||0===this._refcount?0:(0==--this._refcount&&this._setWeak(),this._refcount)}get(e=this.envObject){if(this.persistent.isEmpty())return 0;const t=this.persistent.deref();return e.ensureHandle(t).id}resetFinalizer(){}data(){return 0}refcount(){return this._refcount}ownership(){return this._ownership}callUserFinalizer(){}invokeFinalizerFromGC(){this.finalize()}_setWeak(){this.canBeWeak?this.persistent.setWeak(this,T.weakCallback):this.persistent.reset()}finalize(){this.persistent.reset();const e=this._ownership===L.kRuntime;this.unlink(),this.callUserFinalizer(),e&&this.dispose()}dispose(){0!==this.id&&(this.unlink(),this.persistent.reset(),this.envObject.ctx.refStore.remove(this.id),super.dispose(),this.envObject=void 0,this.id=0)}}class U extends T{static create(e,t,i,n,s){const r=new U(e,t,i,n,s);return e.ctx.refStore.add(r),r.link(e.reflist),r}constructor(e,t,i,n,s){super(e,t,i,n),this._data=s}data(){return this._data}}class P extends T{static create(e,t,i,n,s,r,o){const a=new P(e,t,i,n,s,r,o);return e.ctx.refStore.add(a),a.link(e.finalizing_reflist),a}constructor(e,t,i,n,s,r,o){super(e,t,i,n),this._finalizer=new I(e,s,r,o)}resetFinalizer(){this._finalizer.resetFinalizer()}data(){return this._finalizer.data()}callUserFinalizer(){this._finalizer.callFinalizer()}invokeFinalizerFromGC(){this._finalizer.envObject.invokeFinalizerFromGC(this)}dispose(){this._finalizer&&(this._finalizer.envObject.dequeueFinalizer(this),this._finalizer.dispose(),super.dispose(),this._finalizer=void 0)}}class G{static create(e,t){const i=new G(e,t);return e.deferredStore.add(i),i}constructor(e,t){this.id=0,this.ctx=e,this.value=t}resolve(e){this.value.resolve(e),this.dispose()}reject(e){this.value.reject(e),this.dispose()}dispose(){this.ctx.deferredStore.remove(this.id),this.id=0,this.value=null,this.ctx=null}}class q{constructor(){this._values=[void 0],this._values.length=4,this._size=1,this._freeList=[]}add(e){let t;if(this._freeList.length)t=this._freeList.shift();else{t=this._size,this._size++;const e=this._values.length;t>=e&&(this._values.length=e+(e>>1)+16)}e.id=t,this._values[t]=e}get(e){return this._values[e]}has(e){return void 0!==this._values[e]}remove(e){const t=this._values[e];t&&(t.id=0,this._values[e]=void 0,this._freeList.push(Number(e)))}dispose(){for(let e=1;e<this._size;++e){const t=this._values[e];null==t||t.dispose()}this._values=[void 0],this._size=1,this._freeList=[]}}class V{constructor(e,t,i,n){this.envObject=e,this.fn=t,this.arg=i,this.order=n}}class ${constructor(){this._cleanupHooks=[],this._cleanupHookCounter=0}empty(){return 0===this._cleanupHooks.length}add(e,t,i){if(this._cleanupHooks.filter((n=>n.envObject===e&&n.fn===t&&n.arg===i)).length>0)throw new Error("Can not add same fn and arg twice");this._cleanupHooks.push(new V(e,t,i,this._cleanupHookCounter++))}remove(e,t,i){for(let n=0;n<this._cleanupHooks.length;++n){const s=this._cleanupHooks[n];if(s.envObject===e&&s.fn===t&&s.arg===i)return void this._cleanupHooks.splice(n,1)}}drain(){const e=this._cleanupHooks.slice();e.sort(((e,t)=>t.order-e.order));for(let t=0;t<e.length;++t){const i=e[t];"number"==typeof i.fn?i.envObject.makeDynCall_vp(i.fn)(i.arg):i.fn(i.arg),this._cleanupHooks.splice(this._cleanupHooks.indexOf(i),1)}}dispose(){this._cleanupHooks.length=0,this._cleanupHookCounter=0}}class J{constructor(){this.refHandle=(new f).port1,this.count=0}increase(){0===this.count&&this.refHandle.ref(),this.count++}decrease(){0!==this.count&&(1===this.count&&this.refHandle.unref(),this.count--)}}class Q{constructor(){this._isStopping=!1,this._canCallIntoJs=!0,this._suppressDestroy=!1,this.envStore=new q,this.scopeStore=new F,this.refStore=new q,this.deferredStore=new q,this.handleStore=new C,this.cbinfoStack=new n,this.feature={supportReflect:l,supportFinalizer:c,supportWeakSymbol:u,supportBigInt:h,supportNewFunction:s,canSetFunctionName:a,setImmediate:_,Buffer:v,MessageChannel:f},this.cleanupQueue=new $,"object"==typeof process&&null!==process&&"function"==typeof process.once&&(this.refCounter=new J,process.once("beforeExit",(()=>{this._suppressDestroy||this.destroy()})))}suppressDestroy(){this._suppressDestroy=!0}getRuntimeVersions(){return{version:g,NODE_API_SUPPORTED_VERSION_MAX:9,NAPI_VERSION_EXPERIMENTAL:b,NODE_API_DEFAULT_MODULE_API_VERSION:8}}createNotSupportWeakRefError(e,t){return new B(e,t)}createNotSupportBufferError(e,t){return new A(e,t)}createReference(e,t,i,n){return T.create(e,t,i,n)}createReferenceWithData(e,t,i,n,s){return U.create(e,t,i,n,s)}createReferenceWithFinalizer(e,t,i,n,s=0,r=0,o=0){return P.create(e,t,i,n,s,r,o)}createDeferred(e){return G.create(this,e)}createEnv(e,t,i,n,s,r){return H(this,e,t,i,n,s,r)}createTrackedFinalizer(e,t,i,n){return D.create(e,t,i,n)}getCurrentScope(){return this.scopeStore.currentScope}addToCurrentScope(e){return this.scopeStore.currentScope.add(e)}openScope(e){return this.scopeStore.openScope(e)}closeScope(e,t){this.scopeStore.closeScope(e)}ensureHandle(e){switch(e){case void 0:return C.UNDEFINED;case null:return C.NULL;case!0:return C.TRUE;case!1:return C.FALSE;case r:return C.GLOBAL}return this.addToCurrentScope(e)}addCleanupHook(e,t,i){this.cleanupQueue.add(e,t,i)}removeCleanupHook(e,t,i){this.cleanupQueue.remove(e,t,i)}runCleanup(){for(;!this.cleanupQueue.empty();)this.cleanupQueue.drain()}increaseWaitingRequestCounter(){var e;null===(e=this.refCounter)||void 0===e||e.increase()}decreaseWaitingRequestCounter(){var e;null===(e=this.refCounter)||void 0===e||e.decrease()}setCanCallIntoJs(e){this._canCallIntoJs=e}setStopping(e){this._isStopping=e}canCallIntoJs(){return this._canCallIntoJs&&!this._isStopping}destroy(){this.setStopping(!0),this.setCanCallIntoJs(!1),this.runCleanup()}}let X;function K(){return new Q}function Y(){return X||(X=K()),X}export{t as CallbackInfo,n as CallbackInfoStack,m as ConstHandle,Q as Context,G as Deferred,R as EmnapiError,j as Env,I as Finalizer,w as Handle,x as HandleScope,C as HandleStore,b as NAPI_VERSION_EXPERIMENTAL,k as NODE_API_DEFAULT_MODULE_API_VERSION,z as NODE_API_SUPPORTED_VERSION_MAX,y as NODE_API_SUPPORTED_VERSION_MIN,N as NodeEnv,A as NotSupportBufferError,B as NotSupportWeakRefError,W as Persistent,E as RefTracker,T as Reference,L as ReferenceOwnership,U as ReferenceWithData,P as ReferenceWithFinalizer,F as ScopeStore,q as Store,D as TrackedFinalizer,o as TryCatch,K as createContext,Y as getDefaultContext,p as isReferenceType,g as version};
