#!/bin/bash

echo "🔍 ANALYZING FRONTEND FILE USAGE..."
echo "=================================="

# Function to check if a file/component is imported anywhere
check_usage() {
    local file_pattern="$1"
    local description="$2"
    
    echo ""
    echo "📁 Checking: $description"
    echo "Pattern: $file_pattern"
    
    # Search for imports in TypeScript/JavaScript files
    local usage_count=$(find src/ -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | xargs grep -l "$file_pattern" 2>/dev/null | wc -l)
    
    if [ $usage_count -gt 0 ]; then
        echo "✅ USED ($usage_count files)"
        find src/ -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | xargs grep -l "$file_pattern" 2>/dev/null | head -5
    else
        echo "❌ NOT USED - SAFE TO DELETE"
    fi
}

echo "🎯 ANALYZING DEMO COMPONENTS..."

# Check demo components
check_usage "Charts/" "Demo Charts Components"
check_usage "FormElements/" "Demo Form Elements"
check_usage "CalenderBox" "Calendar Box Component"
check_usage "ui-elements/" "Demo UI Elements"

echo ""
echo "🎯 ANALYZING DEMO PAGES..."

# Check demo pages
check_usage "charts/" "Demo Charts Pages"
check_usage "forms/" "Demo Forms Pages"
check_usage "tables/" "Demo Tables Pages (not our custom tables)"
check_usage "ui-elements/" "Demo UI Elements Pages"

echo ""
echo "🎯 ANALYZING DEMO ASSETS..."

# Check demo assets
check_usage "us-aea-en.js" "Demo JavaScript File"
check_usage "country.ts" "Country Utils"

echo ""
echo "🎯 ANALYZING FONTS..."

# Check font usage
echo "📁 Font files in src/fonts/:"
ls -la src/fonts/ | wc -l
echo "Note: Check if all Satoshi font variants are needed"

echo ""
echo "🎯 SUMMARY RECOMMENDATIONS:"
echo "=========================="
echo "1. ✅ KEEP: Dashboard, Product, Profile, TopUp, Transactions, History"
echo "2. ❌ REMOVE: Charts demo, Forms demo, UI Elements demo"
echo "3. 🔄 REORGANIZE: Rename folders for better structure"
echo "4. 🧹 CLEANUP: Remove unused imports and dependencies"

echo ""
echo "📋 NEXT STEPS:"
echo "1. Review the analysis above"
echo "2. Backup your frontend folder"
echo "3. Start with removing clearly unused demo files"
echo "4. Test after each major change"

echo ""
echo "⚠️  IMPORTANT: Always test functionality after removing files!"
