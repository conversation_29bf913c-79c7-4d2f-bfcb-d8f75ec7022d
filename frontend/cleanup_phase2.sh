#!/bin/bash

echo "🔄 FRONTEND CLEANUP - FASE 2: Reorganisasi Struktur"
echo "=================================================="

echo "📁 Reorganizing folder structure for better navigation..."

# Rename folders untuk konsistensi
echo ""
echo "🏷️ Renaming folders for consistency..."

# Rename product -> products (lebih konsisten)
if [ -d "src/app/(main)/product" ] && [ ! -d "src/app/(main)/products" ]; then
    mv src/app/(main)/product src/app/(main)/products
    echo "✅ Renamed: product -> products"
fi

# Rename detail -> account (lebih jelas)
if [ -d "src/app/(main)/detail" ] && [ ! -d "src/app/(main)/account" ]; then
    mv src/app/(main)/detail src/app/(main)/account
    echo "✅ Renamed: detail -> account"
fi

# Reorganize billing-related pages
echo ""
echo "💰 Organizing billing pages..."

if [ ! -d "src/app/(main)/billing" ]; then
    mkdir -p src/app/(main)/billing
    echo "✅ Created: billing folder"
    
    # Move top-up to billing
    if [ -d "src/app/(main)/top-up" ]; then
        mv src/app/(main)/top-up src/app/(main)/billing/top-up
        echo "✅ Moved: top-up -> billing/top-up"
    fi
    
    # Move transactions to billing
    if [ -d "src/app/(main)/transactions" ]; then
        mv src/app/(main)/transactions src/app/(main)/billing/transactions
        echo "✅ Moved: transactions -> billing/transactions"
    fi
    
    # Move history to billing
    if [ -d "src/app/(main)/history" ]; then
        mv src/app/(main)/history src/app/(main)/billing/history
        echo "✅ Moved: history -> billing/history"
    fi
fi

# Rename component folders
echo ""
echo "🧩 Renaming component folders..."

# Rename Auth -> auth (lowercase consistency)
if [ -d "src/components/Auth" ] && [ ! -d "src/components/auth" ]; then
    mv src/components/Auth src/components/auth
    echo "✅ Renamed: Auth -> auth"
fi

# Rename Layouts -> layout
if [ -d "src/components/Layouts" ] && [ ! -d "src/components/layout" ]; then
    mv src/components/Layouts src/components/layout
    echo "✅ Renamed: Layouts -> layout"
fi

# Rename Dashboard -> dashboard
if [ -d "src/components/Dashboard" ] && [ ! -d "src/components/dashboard" ]; then
    mv src/components/Dashboard src/components/dashboard
    echo "✅ Renamed: Dashboard -> dashboard"
fi

# Rename Tables -> tables
if [ -d "src/components/Tables" ] && [ ! -d "src/components/tables" ]; then
    mv src/components/Tables src/components/tables
    echo "✅ Renamed: Tables -> tables"
fi

# Rename TopUp -> billing (merge with billing concept)
if [ -d "src/components/TopUp" ] && [ ! -d "src/components/billing" ]; then
    mv src/components/TopUp src/components/billing
    echo "✅ Renamed: TopUp -> billing"
fi

# Rename css -> styles
if [ -d "src/css" ] && [ ! -d "src/styles" ]; then
    mv src/css src/styles
    echo "✅ Renamed: css -> styles"
fi

echo ""
echo "📋 Creating organized structure summary..."

# Create structure documentation
cat > FRONTEND_STRUCTURE.md << 'EOF'
# 📁 FRONTEND STRUCTURE (After Cleanup)

## 🎯 Organized Structure

```
frontend/src/
├── app/
│   ├── (auth)/                    # Authentication pages
│   ├── (main)/
│   │   ├── dashboard/             # Main dashboard
│   │   ├── products/              # VPN products (trojan, vless, vmess)
│   │   ├── account/               # Account details & management
│   │   ├── billing/               # All billing-related pages
│   │   │   ├── top-up/           # Top-up saldo
│   │   │   ├── transactions/     # Transaction history
│   │   │   └── history/          # Service history
│   │   ├── profile/              # User profile
│   │   ├── notifications/        # User notifications
│   │   └── admin/                # Admin panel
│   └── api/                      # API routes
├── components/
│   ├── auth/                     # Authentication components
│   ├── layout/                   # Layout components (header, sidebar, footer)
│   ├── dashboard/                # Dashboard-specific components
│   ├── products/                 # Product-related components
│   ├── billing/                  # Billing & payment components
│   ├── tables/                   # Reusable table components
│   ├── ui/                       # Core UI components
│   └── shared/                   # Shared/common components
├── lib/                          # Utilities & configurations
├── hooks/                        # Custom React hooks
├── contexts/                     # React contexts
├── services/                     # API services
├── types/                        # TypeScript type definitions
├── styles/                       # CSS & styling files
└── assets/                       # Static assets
```

## 🎯 Navigation Guide

### For VPN Products:
- **Products**: `src/app/(main)/products/`
- **Components**: `src/components/products/`

### For Billing:
- **Pages**: `src/app/(main)/billing/`
- **Components**: `src/components/billing/`

### For User Management:
- **Profile**: `src/app/(main)/profile/`
- **Account Details**: `src/app/(main)/account/`

### For Admin:
- **Admin Pages**: `src/app/(main)/admin/`
- **Admin Components**: Mixed in relevant component folders

## 🔧 After Reorganization

1. Update all import paths
2. Update routing configurations
3. Test all functionality
4. Update documentation
EOF

echo "✅ Created: FRONTEND_STRUCTURE.md"

echo ""
echo "✅ FASE 2 SELESAI!"
echo ""
echo "📋 YANG SUDAH DIREORGANISASI:"
echo "- product -> products"
echo "- detail -> account"
echo "- Billing pages grouped together"
echo "- Component folders renamed to lowercase"
echo "- css -> styles"
echo ""
echo "⚠️  IMPORTANT: UPDATE IMPORT PATHS!"
echo "Next step: Run update_imports.sh to fix all import paths"
